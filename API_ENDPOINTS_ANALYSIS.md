# DinePulse API Endpoints Analysis

## Manager Login Status

✅ **Manager Login Endpoint EXISTS**

**Backend Endpoint**: `POST /api/auth/login/` (on tenant domains)

- **File**: `backend/tenants/api/views.py` - `TenantTokenObtainPairView`
- **Authentication**: JWT-based
- **Domain**: Tenant-specific (e.g., `restaurant1.localhost:8000/api/auth/login/`)
- **Roles Supported**: MANAGER, WAITER, DELIVERY, SUPERUSER

**Frontend Implementation**: ⚠️ **MISMATCH DETECTED**

- **File**: `frontend/src/components/manager-login-form.jsx`
- **Expected Endpoint**: `POST /api/public/manager-auth/` (NOT FOUND in backend)
- **Status**: Frontend expects different endpoint than backend provides

---

## Complete Backend API Endpoints

### 1. Public Schema Endpoints (`/api/public/`)

#### Restaurant Management

| Endpoint                           | Method | View                       | Frontend Status    |
| ---------------------------------- | ------ | -------------------------- | ------------------ |
| `/api/public/restaurants/`         | GET    | `PublicTenantViewSet.list` | ❌ Not Implemented |
| `/api/public/register-restaurant/` | POST   | `RegisterRestaurantView`   | ❌ Not Implemented |
| `/api/public/billing-plans/`       | GET    | `BillingPlanViewSet.list`  | ❌ Not Implemented |

#### Customer Authentication

| Endpoint                                             | Method   | View                         | Frontend Status               |
| ---------------------------------------------------- | -------- | ---------------------------- | ----------------------------- |
| `/api/public/customer/register/`                     | POST     | `CustomerRegistrationView`   | 🔄 Partially (commented code) |
| `/api/public/customer/login/`                        | POST     | `TokenObtainPairView`        | 🔄 Partially (commented code) |
| `/api/public/customer/token/refresh/`                | POST     | `CustomerTokenRefreshView`   | ❌ Not Implemented            |
| `/api/public/customer/restaurants/`                  | GET      | `AllRestaurantsViewSet.list` | ❌ Not Implemented            |
| `/api/public/customer/favorite-restaurants/`         | GET/POST | `FavoriteRestaurantView`     | ❌ Not Implemented            |
| `/api/public/customer/tenant-token/<int:tenant_id>/` | GET      | `TenantAuthTokenView`        | ❌ Not Implemented            |

### 2. Tenant Schema Endpoints (`/api/auth/`)

#### Authentication

| Endpoint                   | Method | View                        | Frontend Status                                   |
| -------------------------- | ------ | --------------------------- | ------------------------------------------------- |
| `/api/auth/login/`         | POST   | `TenantTokenObtainPairView` | ⚠️ Mismatch (expects `/api/public/manager-auth/`) |
| `/api/auth/token/refresh/` | POST   | `TokenRefreshView`          | ❌ Not Implemented                                |
| `/api/auth/exchange/`      | POST   | `CrossDomainLoginView`      | ❌ Not Implemented                                |

#### Staff Management

| Endpoint                    | Method               | View                       | Frontend Status    |
| --------------------------- | -------------------- | -------------------------- | ------------------ |
| `/api/auth/staff/`          | GET/POST             | `StaffViewSet.list/create` | ❌ Not Implemented |
| `/api/auth/staff/<int:pk>/` | GET/PUT/PATCH/DELETE | `StaffViewSet`             | ❌ Not Implemented |

#### Restaurant Configuration

| Endpoint                   | Method  | View                          | Frontend Status    |
| -------------------------- | ------- | ----------------------------- | ------------------ |
| `/`                        | GET     | `RestaurantHomeView`          | ❌ Not Implemented |
| `/api/auth/config/`        | GET/PUT | `RestaurantConfigurationView` | ❌ Not Implemented |
| `/api/auth/theme/`         | GET/PUT | `RestaurantThemeView`         | ❌ Not Implemented |
| `/api/auth/theme-options/` | GET     | `ThemeOptionsView`            | ❌ Not Implemented |
| `/api/auth/payments/`      | GET     | `PaymentViewSet.list`         | ❌ Not Implemented |
| `/api/auth/notifications/` | GET     | `NotificationViewSet.list`    | ❌ Not Implemented |

#### Menu Management

| Endpoint                              | Method               | View              | Frontend Status    |
| ------------------------------------- | -------------------- | ----------------- | ------------------ |
| `/api/auth/menu/categories/`          | GET/POST             | `CategoryViewSet` | ❌ Not Implemented |
| `/api/auth/menu/categories/<int:pk>/` | GET/PUT/PATCH/DELETE | `CategoryViewSet` | ❌ Not Implemented |
| `/api/auth/menu/menu-items/`          | GET/POST             | `MenuItemViewSet` | ❌ Not Implemented |
| `/api/auth/menu/menu-items/<int:pk>/` | GET/PUT/PATCH/DELETE | `MenuItemViewSet` | ❌ Not Implemented |

#### Order Management

| Endpoint                                                         | Method               | View                                  | Frontend Status    |
| ---------------------------------------------------------------- | -------------------- | ------------------------------------- | ------------------ |
| `/api/auth/orders/list/orders/`                                  | GET/POST             | `OrderViewSet`                        | ❌ Not Implemented |
| `/api/auth/orders/list/orders/<int:pk>/`                         | GET/PUT/PATCH/DELETE | `OrderViewSet`                        | ❌ Not Implemented |
| `/api/auth/orders/list/orders/<int:pk>/assign_delivery/`         | POST                 | `OrderViewSet.assign_delivery`        | ❌ Not Implemented |
| `/api/auth/orders/list/orders/free_delivery_persons/`            | GET                  | `OrderViewSet.free_delivery_persons`  | ❌ Not Implemented |
| `/api/auth/orders/list/orders/<int:pk>/update_status/`           | PUT                  | `OrderViewSet.update_status`          | ❌ Not Implemented |
| `/api/auth/orders/list/delivery-orders/`                         | GET                  | `DeliveryOrderViewSet`                | ❌ Not Implemented |
| `/api/auth/orders/list/delivery-orders/<int:pk>/mark_delivered/` | POST                 | `DeliveryOrderViewSet.mark_delivered` | ❌ Not Implemented |
| `/api/auth/orders/list/payments/`                                | GET                  | `PaymentTransactionViewSet`           | ❌ Not Implemented |
| `/api/auth/orders/cart/`                                         | GET/DELETE           | `CartView`                            | ❌ Not Implemented |
| `/api/auth/orders/cart/items/`                                   | POST                 | `CartItemView`                        | ❌ Not Implemented |
| `/api/auth/orders/cart/items/<int:item_id>/`                     | PUT/DELETE           | `CartItemView`                        | ❌ Not Implemented |
| `/api/auth/orders/guest/orders/`                                 | POST                 | `GuestOrderCreateView`                | ❌ Not Implemented |
| `/api/auth/orders/guest/orders/status/`                          | GET                  | `GuestOrderStatusView`                | ❌ Not Implemented |

### 3. Admin Endpoints

| Endpoint  | Method | View         | Frontend Status    |
| --------- | ------ | ------------ | ------------------ |
| `/admin/` | GET    | Django Admin | ❌ Not Implemented |

---

## Frontend Implementation Summary

### ✅ Implemented Endpoints (3)

1. Admin login (expects `/api/public/admin/login/` - NOT FOUND in backend)
2. Manager login (expects `/api/public/manager-auth/` - NOT FOUND in backend)
3. Customer login/register (partially implemented, commented out)

### ❌ Missing Frontend Implementation (40+ endpoints)

- All staff management endpoints
- All restaurant configuration endpoints
- All menu management endpoints
- All order management endpoints
- All cart management endpoints
- Customer authentication endpoints
- Restaurant listing endpoints
- Billing plan endpoints

### ⚠️ Critical Issues Found

1. **Manager Login Mismatch**: Frontend expects `/api/public/manager-auth/` but backend provides `/api/auth/login/`
2. **Admin Login Mismatch**: Frontend expects `/api/public/admin/login/` but no such endpoint exists
3. **Missing Public Manager Auth**: Backend has no `/api/public/manager-auth/` endpoint
4. **Incomplete Customer Auth**: Customer login/register code is commented out

---

## Recommendations

### Immediate Actions Required

1. **Fix Manager Login**: Update frontend to use correct endpoint `/api/auth/login/` on tenant domains
2. **Implement Missing Admin Endpoint**: Create `/api/public/admin/login/` in backend or update frontend
3. **Complete Customer Auth**: Uncomment and test customer authentication flows
4. **Add API Service Layer**: Create centralized API service in frontend for all endpoints

### Next Steps

1. Implement frontend components for all missing endpoints
2. Add proper error handling and loading states
3. Implement authentication token management
4. Add API documentation and testing

---

## Detailed Endpoint Analysis

### Authentication Flow Issues

#### Current Manager Login Process (Frontend)

```javascript
// File: frontend/src/components/manager-login-form.jsx
const authRes = await fetch(`${baseUrl}api/public/manager-auth/`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    restaurant_id: restaurantId,
    username: username,
    password: password,
  }),
});
```

#### Actual Backend Manager Login (Backend)

```python
# File: backend/tenants/api/views.py
# Endpoint: POST /api/auth/login/ (on tenant domain)
class TenantTokenObtainPairView(TokenObtainPairView):
    def post(self, request, *args, **kwargs):
        # Validates user belongs to current tenant
        # Returns JWT tokens for authentication
```

#### Required Fix

Frontend should use tenant-specific domain and correct endpoint:

```javascript
// Correct implementation needed:
const tenantDomain = `${restaurantId}.localhost:8000`;
const authRes = await fetch(`http://${tenantDomain}/api/auth/login/`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    username: username,
    password: password,
  }),
});
```

### Missing Critical Endpoints

#### 1. Public Manager Authentication

**Status**: ❌ **MISSING FROM BACKEND**

- Frontend expects: `POST /api/public/manager-auth/`
- Backend provides: Only tenant-specific login
- **Action Required**: Create public manager auth endpoint or update frontend

#### 2. Admin Authentication

**Status**: ❌ **MISSING FROM BACKEND**

- Frontend expects: `POST /api/public/admin/login/`
- Backend provides: Only Django admin interface
- **Action Required**: Create admin API endpoint

#### 3. Customer Cross-Domain Authentication

**Status**: ✅ **EXISTS BUT NOT USED**

- Backend provides: `GET /api/public/customer/tenant-token/<int:tenant_id>/`
- Purpose: Generate tokens for customers to access tenant domains
- Frontend status: Not implemented

### Permission Classes Used

#### Backend Permission Classes

1. `IsRestaurantManager` - Manager access to their restaurant
2. `IsRegisteredCustomer` - Customer-specific access
3. `IsPublicTenant` - Public schema access
4. `IsStaffOrCustomer` - Staff or customer access
5. `IsWaiter` - Waiter-specific permissions
6. `IsWaiterOrManager` - Waiter or manager access
7. `IsDeliveryPerson` - Delivery person access

### API Response Patterns

#### Authentication Responses

```json
// Tenant Login Response
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token"
}

// Customer Login Response
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "role": "customer"
  }
}
```

#### Error Response Pattern

```json
{
  "detail": "Error message",
  "error": "Error description"
}
```

### Frontend API Integration Patterns

#### Current Implementation Issues

1. **Hardcoded URLs**: Multiple hardcoded backend URLs
2. **No Centralized API Service**: Each component makes direct fetch calls
3. **Inconsistent Error Handling**: No standardized error handling
4. **Missing Token Management**: No automatic token refresh
5. **No Request Interceptors**: No automatic auth header injection

#### Recommended API Service Structure

```javascript
// File: frontend/src/lib/api.js (needs to be created)
class ApiService {
  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    this.publicUrl = `${this.baseUrl}/api/public`;
  }

  // Public endpoints
  async customerLogin(credentials) {
    /* ... */
  }
  async customerRegister(data) {
    /* ... */
  }
  async getRestaurants() {
    /* ... */
  }

  // Tenant endpoints
  async managerLogin(tenantDomain, credentials) {
    /* ... */
  }
  async getMenuItems(tenantDomain) {
    /* ... */
  }
  async createOrder(tenantDomain, orderData) {
    /* ... */
  }

  // Admin endpoints
  async adminLogin(credentials) {
    /* ... */
  }
}
```

---

## Implementation Priority Matrix

### High Priority (Critical for Basic Functionality)

1. **Fix Manager Login** - Update frontend to use correct endpoint
2. **Implement Customer Authentication** - Complete login/register flows
3. **Create API Service Layer** - Centralize all API calls
4. **Add Missing Admin Endpoint** - Create admin authentication

### Medium Priority (Core Features)

1. **Menu Management** - CRUD operations for categories and items
2. **Order Management** - Order creation, status updates, delivery assignment
3. **Staff Management** - Create, update, delete staff members
4. **Restaurant Configuration** - Theme, settings, payment options

### Low Priority (Advanced Features)

1. **Analytics Endpoints** - Dashboard statistics and reports
2. **Notification System** - Real-time notifications
3. **Payment Integration** - Payment processing endpoints
4. **Advanced Filtering** - Search and filter capabilities

---

## Testing Recommendations

### Backend Testing

1. Test all authentication endpoints with different user roles
2. Verify tenant isolation (users can only access their restaurant data)
3. Test permission classes with various user types
4. Validate API response formats and error handling

### Frontend Testing

1. Test authentication flows with real backend
2. Verify token storage and automatic refresh
3. Test error handling for network failures
4. Validate responsive design across devices

### Integration Testing

1. End-to-end user workflows (customer ordering, manager processing)
2. Cross-domain authentication flows
3. Real-time features (order status updates)
4. Payment processing workflows
