# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django specific
*.log
*.pot
*.pyc
*.sqlite3
db.sqlite3
media/
staticfiles/
node_modules/
.env
.env.*
venv/
venv*/
ENV/
env/
.envrc
*.DS_Store
*.swp
*.swo
*.coveragerc
coverage.xml
htmlcov/
.tox/
.pytest_cache/
nosetests.xml
coverage/
*.mo

# Django migrations
*/migrations/*.pyc
*/migrations/*.py
!*/migrations/__init__.py

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.iws

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs and databases
*.log
*.sqlite3
*.db

# Sentry
.sentryclirc

# Jupyter Notebook
.ipynb_checkpoints

# Pytest
.cache/
pytest_cache/

# Coverage reports
.coverage
coverage.xml
htmlcov/

# Static files
staticfiles/
media/

# Node/npm
node_modules/
package-lock.json
yarn.lock

# Python virtual environment
.env/
env/
venv/
ENV/

# Other files you may want to ignore
*.bak
*.tmp
*.swp
