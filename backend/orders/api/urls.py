from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from orders.api.views import (
    OrderViewSet, 
    DeliveryOrderViewSet, 
    PaymentTransactionViewSet,
    GuestOrderCreateView, 
    GuestOrderStatusView,
    CartView, 
    CartItemView
)

router = DefaultRouter()
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'delivery-orders', DeliveryOrderViewSet, basename='delivery-order')
router.register(r'payments', PaymentTransactionViewSet, basename='payment-transactions')

urlpatterns = [
    path('list/', include(router.urls)),
    path('cart/', CartView.as_view(), name='cart-detail'),
    path('cart/items/', CartItemView.as_view(), name='cart-item-list'),
    path('cart/items/<int:item_id>/', CartItemView.as_view(), name='cart-item-detail'),
    path('guest/orders/', GuestOrderCreateView.as_view(), name='guest-order-create'),
    path('guest/orders/status/', GuestOrderStatusView.as_view(), name='guest-order-status'),
]