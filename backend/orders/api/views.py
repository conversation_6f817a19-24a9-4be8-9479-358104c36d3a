from rest_framework import viewsets, permissions, status
from rest_framework.response import Response 
from orders.models import Order, PaymentTransaction, OrderStatus
from orders.api.serializers import OrderSerializer, PaymentTransactionSerializer
from django_tenants.utils import tenant_context
from django.db import transaction, models
from rest_framework.views import APIView
from .serializers import GuestOrderSerializer
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from backend.permissions import (
    IsStaffOrCustomer, 
    IsRestaurantManager, 
    IsWaiter, 
    IsWaiterOrManager, 
    IsDeliveryPerson
)
from users.models import User, UserRole
from rest_framework.decorators import action
from .serializers import (
    OrderSerializer,
    AssignDeliveryPersonSerializer,
    DeliveryOrderSerializer,
    OrderStatusUpdateSerializer
)  
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from orders.models import Cart, CartItem
from orders.api.serializers import CartSerializer, CartItemSerializer
from menu.models import MenuItem
from django.http import Http404
from django.utils import timezone

class CartBaseView(APIView):
    permission_classes = [permissions.AllowAny]

    def get_cart(self, request):
        tenant = request.tenant
        user = request.user if request.user.is_authenticated else None
        session_key = request.session.session_key

        # Create session if doesn't exist
        if not session_key and not user:
            request.session.create()
            session_key = request.session.session_key

        # Get or create cart
        with tenant_context(tenant):
            if user:
                cart, created = Cart.objects.get_or_create(tenant=tenant, user=user)
            else:
                cart, created = Cart.objects.get_or_create(
                    tenant=tenant, 
                    session_key=session_key
                )
        return cart

class CartView(CartBaseView):
    def get(self, request):
        with tenant_context(request.tenant):
            cart = self.get_cart(request)
            serializer = CartSerializer(cart)
            return Response(serializer.data)

    def delete(self, request):
        with tenant_context(request.tenant):
            cart = self.get_cart(request)
            cart.items.all().delete()
            cart.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

class CartItemView(CartBaseView):
    def get_queryset(self, cart):
        return cart.items.all()

    def get_item(self, cart, item_id):
        try:
            return cart.items.get(id=item_id)
        except CartItem.DoesNotExist:
            raise Http404

    def post(self, request):
        with tenant_context(request.tenant):
            cart = self.get_cart(request)
            # Filter menu items to current tenant
            request.data['menu_item_id'] = request.data.get('menu_item_id')
            serializer = CartItemSerializer(
                data=request.data,
                context={
                    'request': request,
                    'menu_item_queryset': MenuItem.objects.filter(restaurant=request.tenant)
                }
            )
            
            if serializer.is_valid():
                menu_item = serializer.validated_data['menu_item']
                
                # Check if item already in cart
                existing_item = cart.items.filter(menu_item=menu_item).first()
                if existing_item:
                    existing_item.quantity += serializer.validated_data['quantity']
                    existing_item.save()
                    return Response(
                        CartItemSerializer(existing_item).data,
                        status=status.HTTP_200_OK
                    )
                else:
                    serializer.save(cart=cart)
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, item_id):
        with tenant_context(request.tenant):
            cart = self.get_cart(request)
            item = self.get_item(cart, item_id)
            serializer = CartItemSerializer(
                item, 
                data=request.data, 
                partial=True,
                context={
                    'request': request,
                    'menu_item_queryset': MenuItem.objects.filter(restaurant=request.tenant)
                }
            )
            
            if serializer.is_valid():
                # Prevent changing menu item
                if 'menu_item_id' in serializer.validated_data:
                    return Response(
                        {"detail": "Cannot change menu item in cart"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, item_id):
        with tenant_context(request.tenant):
            cart = self.get_cart(request)
            item = self.get_item(cart, item_id)
            item.delete()
            
            # Delete cart if empty
            if not cart.items.exists():
                cart.delete()
                
            return Response(status=status.HTTP_204_NO_CONTENT)

class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated, IsStaffOrCustomer]
    filterset_fields = ['status', 'payment_method', 'is_paid']
    queryset = Order.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            if self.request.user.role == UserRole.CUSTOMER:
                return Order.objects.filter(
                    models.Q(customer=self.request.user) |
                    models.Q(guest_email=self.request.user.email) |
                    models.Q(guest_phone=self.request.user.phone_number)
                )
            elif self.request.user.role in [UserRole.MANAGER, UserRole.WAITER, UserRole.DELIVERY]:
                return Order.objects.all()
            return Order.objects.none()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    @transaction.atomic
    def perform_create(self, serializer):
        if self.request.user.role == UserRole.CUSTOMER:
            serializer.save(customer=self.request.user)
        else:
            serializer.save()

    @action(
        detail=True, 
        methods=['post'], 
        permission_classes=[permissions.IsAuthenticated, IsWaiter],
        serializer_class=AssignDeliveryPersonSerializer
    )
    def assign_delivery(self, request, pk=None):
        order = self.get_object()
        if order.status == OrderStatus.DELIVERED:
            return Response(
                {'detail': 'Delivered orders cannot be modified'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = AssignDeliveryPersonSerializer(
            data=request.data, 
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        delivery_person = serializer.validated_data['delivery_person_id']

        order.delivery_Person = delivery_person
        order.status = OrderStatus.ON_DELIVERY
        order.save()
        
        return Response(OrderSerializer(order).data)
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated, IsWaiterOrManager])
    def free_delivery_persons(self, request):
        """
        List available delivery persons for the current restaurant
        who aren't currently assigned to active deliveries
        """
        with tenant_context(self.request.tenant):
            # Get all active delivery persons for current restaurant
            delivery_persons = User.objects.filter(
                role=UserRole.DELIVERY,
                restaurant=request.user.restaurant,
                is_active=True
            )

            # Get IDs of delivery persons currently on active deliveries
            busy_delivery_ids = Order.objects.filter(
                status=OrderStatus.ON_DELIVERY,
                delivery_Person__isnull=False
            ).values_list('delivery_Person_id', flat=True)

            # Filter out busy delivery persons
            free_delivery_persons = delivery_persons.exclude(
                id__in=busy_delivery_ids
            )

            # Serialize results
            data = [{
                'id': person.id,
                'name': person.get_full_name(),
                'phone': str(person.phone_number)
            } for person in free_delivery_persons]

            return Response(data)
        
    @action(
        detail=True,
        methods=['put'],
        permission_classes=[permissions.IsAuthenticated, IsWaiterOrManager],
        serializer_class=OrderStatusUpdateSerializer
    )
    def update_status(self, request, pk=None):
        order = self.get_object()
        
        # Prevent updates to delivered orders
        if order.status == OrderStatus.DELIVERED:
            return Response(
                {'detail': _('Delivered orders cannot be modified')},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        new_status = serializer.validated_data['status']
        
        # Update order status
        order.status = new_status
        order.save()
        
        return Response(
            OrderSerializer(order, context=self.get_serializer_context()).data,
            status=status.HTTP_200_OK
        )

class DeliveryOrderViewSet(viewsets.ReadOnlyModelViewSet):
    """Viewset for delivery persons to manage their orders"""
    serializer_class = DeliveryOrderSerializer
    permission_classes = [permissions.IsAuthenticated, IsDeliveryPerson]
    
    def get_queryset(self):
        # Only orders assigned to the current delivery person
        return Order.objects.filter(
            delivery_Person=self.request.user,
            status__in=[OrderStatus.ON_DELIVERY, OrderStatus.DELIVERED]
        )
    
    @action(detail=True, methods=['post'])
    def mark_delivered(self, request, pk=None):
        """Mark an order as delivered"""
        order = get_object_or_404(
            Order, 
            id=pk, 
            delivery_Person=request.user
        )
        
        # Check current status
        if order.status == OrderStatus.DELIVERED:
            return Response(
                {'detail': 'Order is already delivered'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if order.status != OrderStatus.ON_DELIVERY:
            return Response(
                {'detail': 'Only orders on delivery can be marked as delivered'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update status
        order.status = OrderStatus.DELIVERED
        order.save()
        
        return Response(self.get_serializer(order).data)

class GuestOrderCreateView(APIView):
    permission_classes = [permissions.AllowAny]
    
    @method_decorator(ratelimit(key='ip', rate='5/m', block=True))
    def post(self, request):
        with tenant_context(request.tenant):
            serializer = GuestOrderSerializer(data=request.data)
            if serializer.is_valid():
                order = serializer.save()
                return Response({
                    #'order_id': order.id,
                    'status': order.status,
                    'tracking_code': order.tracking_code,
                    'total_amount': order.total_amount
                }, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class GuestOrderStatusView(APIView):
    permission_classes = [permissions.AllowAny]
    
    @method_decorator(ratelimit(key='ip', rate='10/m', block=True))
    def get(self, request):
        phone = request.query_params.get('phone')
        tracking_code = request.query_params.get('tracking_code')
        
        if not phone or not tracking_code:
            return Response(
                {'error': 'Phone and tracking code required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with tenant_context(request.tenant):
            try:
                order = Order.objects.get(
                    tracking_code=tracking_code,
                    guest_phone=phone,
                    is_guest_order=True
                )
                return Response({...})
            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

class PaymentTransactionViewSet(viewsets.ModelViewSet):
    serializer_class = PaymentTransactionSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]
    queryset = PaymentTransaction.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return PaymentTransaction.objects.filter(order__customer=self.request.user)