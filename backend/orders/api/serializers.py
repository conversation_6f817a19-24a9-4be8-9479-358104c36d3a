from rest_framework import serializers
from orders.models import Order, OrderStatus, OrderItem, PaymentTransaction, PaymentMethod
from users.models import User, UserRole
from users.api.serializers import UserSerializer
from orders.models import Cart, CartItem
from menu.api.serializers import MenuItemSerializer
from menu.models import MenuItem

class CartItemSerializer(serializers.ModelSerializer):
    menu_item = MenuItemSerializer(read_only=True)
    menu_item_id = serializers.PrimaryKeyRelatedField(
        queryset=MenuItem.objects.none(),  # Default empty queryset
        source='menu_item',
        write_only=True
    )
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = CartItem
        fields = ['id', 'menu_item', 'menu_item_id', 'quantity', 'special_instructions', 'total_price']
        extra_kwargs = {
            'quantity': {'min_value': 1}
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set queryset for menu_item_id from context if available
        if 'menu_item_queryset' in self.context:
            self.fields['menu_item_id'].queryset = self.context['menu_item_queryset']

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = Cart
        fields = ['id', 'items', 'total_price', 'created_at', 'updated_at']

class OrderStatusUpdateSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=[
        OrderStatus.CONFIRMED,
        OrderStatus.PREPARING,
        OrderStatus.ON_DELIVERY,
        OrderStatus.CANCELLED,
    ])

    def validate_status(self, value):
        if value in [OrderStatus.PENDING, OrderStatus.DELIVERED]:
            raise serializers.ValidationError(
                _("Cannot update status to PENDING or DELIVERED")
            )
        return value

class OrderSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    delivery_person_details = serializers.SerializerMethodField()
    delivery_location = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'tracking_code', 'status', 'status_display', 
            'created_at', 'total_amount', 'delivery_address',
            'delivery_instructions', 'delivery_Person', 
            'delivery_person_details', 'delivery_location',
            'customer_phone', 'is_paid'
        ]
        read_only_fields = [
            'id', 'tracking_code', 'created_at', 'total_amount',
            'delivery_address', 'customer_phone', 'is_paid',
            'status_display', 'delivery_person_details',
            'delivery_location'
        ]

    def get_delivery_person_details(self, obj):
        if obj.delivery_Person:
            return {
                'id': obj.delivery_Person.id,
                'name': obj.delivery_Person.get_full_name(),
                'phone': str(obj.delivery_Person.phone_number)
            }
        return None

    def get_delivery_location(self, obj):
        if obj.delivery_location:
            return {
                'latitude': obj.delivery_location.y,
                'longitude': obj.delivery_location.x
            }
        return None

    def validate(self, data):
        # Prevent updating delivered orders
        if self.instance and self.instance.status == OrderStatus.DELIVERED:
            raise serializers.ValidationError("Delivered orders cannot be modified")
        
        # When setting status to ON_DELIVERY, delivery_Person must be provided
        if 'status' in data and data['status'] == OrderStatus.ON_DELIVERY:
            if 'delivery_Person' not in data or not data['delivery_Person']:
                raise serializers.ValidationError(
                    "Delivery person is required when status is set to ON_DELIVERY"
                )
            
            # Validate delivery person
            delivery_person = data['delivery_Person']
            if delivery_person.role != UserRole.DELIVERY or delivery_person.restaurant != self.context['request'].user.restaurant:
                raise serializers.ValidationError("Invalid delivery person")
                
        return data

class AssignDeliveryPersonSerializer(serializers.Serializer):
    delivery_person_id = serializers.IntegerField(required=True)
    
    def validate_delivery_person_id(self, value):
        try:
            user = User.objects.get(id=value, role=UserRole.DELIVERY)
            if user.restaurant != self.context['request'].user.restaurant:
                raise serializers.ValidationError("Delivery person not found in your restaurant")
            return user
        except User.DoesNotExist:
            raise serializers.ValidationError("Delivery person not found")

class DeliveryOrderSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    delivery_location = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'tracking_code', 'status', 'status_display', 
            'created_at', 'total_amount', 'delivery_address',
            'delivery_instructions', 'delivery_location',
            'customer_phone'
        ]
        read_only_fields = fields

    def get_delivery_location(self, obj):
        if obj.delivery_location:
            return {
                'latitude': obj.delivery_location.y,
                'longitude': obj.delivery_location.x
            }
        return None

class OrderItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderItem
        fields = ['id', 'menu_item', 'quantity', 'price', 'special_instructions']

class PaymentTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentTransaction
        fields = ['id', 'amount', 'transaction_id', 'payment_method', 'status', 'created_at']

class GuestOrderItemSerializer(serializers.ModelSerializer):
    menu_item_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = OrderItem
        fields = ['menu_item_id', 'quantity', 'special_instructions']

class GuestOrderSerializer(serializers.ModelSerializer):
    items = GuestOrderItemSerializer(many=True)
    
    class Meta:
        model = Order
        fields = [
            'guest_name', 'guest_phone', 'guest_email',
            'delivery_address', 'delivery_instructions',
            'payment_method', 'items'
        ]
        
    def create(self, validated_data):
        from menu.models import MenuItem  # Avoid circular import

        validated_data['is_guest_order'] = True  # Explicitly mark as guest order
        
        items_data = validated_data.pop('items')
        validated_data['is_guest_order'] = True
        total_amount = 0
        order_items_to_create = []

        # Fetch all menu items in one query
        menu_item_ids = [item['menu_item_id'] for item in items_data]
        menu_items = MenuItem.objects.filter(id__in=menu_item_ids)
        menu_item_map = {m.id: m for m in menu_items}

        # Validate all menu items exist
        missing_ids = set(menu_item_ids) - set(menu_item_map.keys())
        if missing_ids:
            raise serializers.ValidationError(
                {"items": f"Invalid menu item IDs: {', '.join(map(str, missing_ids))}"}
            )

        # Calculate total and prepare OrderItem instances
        for item_data in items_data:
            menu_item = menu_item_map[item_data['menu_item_id']]
            quantity = item_data['quantity']
            price = menu_item.price
            total_amount += price * quantity
            
            order_items_to_create.append(OrderItem(
                menu_item=menu_item,
                quantity=quantity,
                price=price,
                special_instructions=item_data.get('special_instructions', '')
            ))

        # Set total amount BEFORE creating order
        validated_data['total_amount'] = total_amount
        order = Order.objects.create(**validated_data)
        
        # Assign order to items and bulk create
        for item in order_items_to_create:
            item.order = order
        OrderItem.objects.bulk_create(order_items_to_create)
        
        return order