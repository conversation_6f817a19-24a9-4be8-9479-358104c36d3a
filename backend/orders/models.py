from django.db import models
from django.contrib.gis.db import models as gis_models
from django.utils.translation import gettext_lazy as _ 
from django.core.validators import MinValueValidator
import shortuuid
from django.conf import settings
from django_tenants.models import TenantMixin
from menu.models import MenuItem
from tenants.models import Client
from users.models import User

class OrderStatus(models.TextChoices):
    PENDING = 'pending', _('Pending')
    CONFIRMED = 'confirmed', _('Confirmed')
    PREPARING = 'preparing', _('Preparing')
    ON_DELIVERY = 'on_delivery', _('On Delivery')
    DELIVERED = 'delivered', _('Delivered')
    CANCELLED = 'cancelled', _('Cancelled')

class PaymentMethod(models.TextChoices):
    CASH = 'cash', _('Cash')
    CARD = 'card', _('Card')
    ONLINE = 'online', _('Online')
    MOBILE_MONEY = 'mobile money', _('Mobile Money')

def generate_tracking_code():
    return shortuuid.ShortUUID().random(10).upper()

class Order(models.Model):
    # Customer field is optional optional
    customer = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,  # Allow blank for guest orders
        related_name='orders'
    )
    status = models.CharField(
        max_length=20,
        choices=OrderStatus.choices,
        default=OrderStatus.PENDING
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        default=PaymentMethod.CASH
    )
    is_paid = models.BooleanField(default=False)
    delivery_address = models.TextField()
    delivery_instructions = models.TextField(blank=True)
    delivery_Person = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='deliveries'
    )
    customer_phone = models.CharField(max_length=20)

    is_guest_order = models.BooleanField(default=False)
    guest_phone = models.CharField(max_length=20, blank=True, null=True)
    guest_name = models.CharField(max_length=255, blank=True, null=True)
    guest_email = models.EmailField(blank=True, null=True)

    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_orders'
    )

    tracking_code = models.CharField(
        max_length=10,
        unique=True,
        default=generate_tracking_code,
        editable=False
    )
    delivery_location = gis_models.PointField(
        geography=True, 
        srid=4326, 
        blank=True, 
        null=True
    ) 

    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Order #{self.id} {self.get_status_display()}"
    
    def save(self, *args, **kwargs):
        # Generate tracking code if missing
        if not self.tracking_code:
            self.tracking_code = generate_tracking_code()
        super().save(*args, **kwargs)
    
    def order_items(self):
        return self.orderitem_set.all()

class OrderItem(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='order_items'
    )
    menu_item = models.ForeignKey(
        'menu.MenuItem',
        on_delete=models.PROTECT,
        related_name='order_items'
    )
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    special_instructions = models.TextField(blank=True)

    @property
    def total_price(self):
        return self.quantity * self.price
    
    def __str__(self):
        return f"{self.quantity}x {self.menu_items.name} for  Order # {self.order.id}"

class PaymentTransaction(models.Model):
    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='payment_transaction'
    )
    amount = models.DecimalField(decimal_places=2, max_digits=10)
    transaction_id = models.CharField(max_length=100)
    payment_method = models.CharField(max_length=50)
    status = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    processes_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment for Order #{self.order.id}"
    
class Cart(models.Model):
    tenant = models.ForeignKey(Client, on_delete=models.CASCADE)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='carts'
    )
    session_key = models.CharField(max_length=40, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = (('tenant', 'user'), ('tenant', 'session_key'))
        ordering = ['-updated_at']

    def __str__(self):
        if self.user:
            return f"Cart for {self.user.email} at {self.tenant.restaurant_name}"
        return f"Guest cart at {self.tenant.restaurant_name}"

    @property
    def total_price(self):
        return sum(item.total_price for item in self.items.all())

class CartItem(models.Model):
    cart = models.ForeignKey(Cart, related_name='items', on_delete=models.CASCADE)
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    special_instructions = models.TextField(blank=True, null=True)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('cart', 'menu_item')
        ordering = ['added_at']

    def __str__(self):
        return f"{self.quantity}x {self.menu_item.name}"

    @property
    def total_price(self):
        return self.quantity * self.menu_item.price