from django.db import models
from tenants.models import Client
from django.utils.translation import gettext_lazy as _
from datetime import timedelta
from django.utils.timezone import now

class BillingPlanChoice(models.TextChoices):
    FREE_TRIAL = 'free_trial', _('Free Trial')
    BASIC = 'basic', _('Basic')
    STANDARD = 'standard', _('Standard')
    PREMIUM = 'premium', _('Premium')

class BillingPlan(models.Model):
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Set 0.00 for Free/Trial Plan.")
    api_limit = models.PositiveIntegerField(default=1000)
    max_staff = models.PositiveIntegerField(default=5)
    duration_months = models.PositiveIntegerField(default=1)
    plan_type = models.CharField(max_length=20, choices=BillingPlanChoice.choices, default='basic')
    is_trial = models.BooleanField(default=False)
    trial_duration_days = models.PositiveIntegerField(default=14)
    features = models.JSONField(blank=True, null=True, help_text="JSON format e.g. {\"reporting\": true, \"pos\": false}")

    def __str__(self):
        return f"{self.name} ({self.plan_type})"

    def get_trial_expiry_date(self):
        return timedelta(days=self.trial_duration_days) if self.is_trial else None

class Subscription(models.Model):
    STATUS_CHOICES = [
        ('trial', 'Trial'),
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('paused', 'Paused'),
        ('canceled', 'Canceled'),
    ]

    tenant = models.OneToOneField(Client, on_delete=models.CASCADE, related_name="subscription")
    plan = models.ForeignKey(BillingPlan, on_delete=models.CASCADE)
    start_date = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    def save(self, *args, **kwargs):
        if self.plan.is_trial and not self.expires_at:
            self.expires_at = self.start_date + self.plan.get_trial_expiry_date()
            self.status = 'trial'
        super().save(*args, **kwargs)

    def is_expired(self):
        return self.expires_at and self.expires_at < now()

    def __str__(self):
        return f"{self.tenant.name} - {self.plan.name} ({self.status})"

class Invoice(models.Model):
    tenant = models.ForeignKey(Client, on_delete=models.CASCADE)
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2) # FOREIGN KEY FROM SUBSCRIPTION PLAN
    issued_date = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField(blank=True, null=True)
    is_paid = models.BooleanField(default=False)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    receipt_file = models.FileField(upload_to='invoices/', blank=True, null=True)

    def __str__(self):
        return f"Invoice {self.id} - {self.tenant.name}"


class Refund(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField(blank=True, null=True)
    refunded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Refund for Invoice {self.invoice.id}"


class Coupon(models.Model):
    code = models.CharField(max_length=50, unique=True)
    discount_percent = models.PositiveIntegerField(default=10)
    expires_at = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    used = models.BooleanField(default=False)
    # billing_plan = models.ForeignKey(BillingPlan, on_delete=models.SET)

    def __str__(self):
        return f"{self.code} - {self.discount_percent}%"

    def is_valid(self):
        return self.is_active and not self.used and (self.expires_at is None or self.expires_at >= now())


class PlanUsage(models.Model):
    tenant = models.OneToOneField(Client, on_delete=models.CASCADE)
    month = models.DateField()
    staff_count = models.PositiveIntegerField(default=0)
    api_calls = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"Usage - {self.tenant.name} - {self.month}"
    
    @property
    def is_active(self):
        return self.status in ['active', 'trial'] and not self.is_expired()