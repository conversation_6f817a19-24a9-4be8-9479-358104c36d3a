from django.utils.text import slugify
from rest_framework import serializers
from tenants.models import PendingClient, Client, BillingPlanOption
from billing.models import BillingPlan

class BillingPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = BillingPlan
        fields = [
            'name', 'description', 'price', 'api_limit', 'max_staff',
            'features'
        ]

class PendingClientSerializer(serializers.ModelSerializer):
    # Add billing_plan field
    billing_plan = serializers.ChoiceField(choices=BillingPlanOption.choices)

    class Meta:
        model = PendingClient
        fields = [
            'restaurant_name', 'manager_email',
            'manager_firstname', 'manager_lastname', 'manager_phone',
            'restaurant_phone', 'location', 'description',
            'address', 'logo', 'banner', 'billing_plan'  # Add field
        ]

    def create(self, validated_data):
        base_schema = slugify(validated_data['restaurant_name']).replace('-', '_')[:63].lower()
        schema_name = base_schema
        counter = 1
        
        while PendingClient.objects.filter(schema_name=schema_name).exists() or \
              Client.objects.filter(schema_name=schema_name).exists():
            schema_name = f"{base_schema}_{counter}"
            counter += 1
        
        validated_data['schema_name'] = schema_name
        return super().create(validated_data)

class PublicTenantSerializer(serializers.ModelSerializer):
    domain = serializers.CharField()  # Serialize annotated domain
    favorite_count = serializers.IntegerField()
    full_domain_url = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = [
            'id', 'restaurant_name', 'description', 'logo', 'banner', 
            'favorite_count', 'domain', 'full_domain_url'
        ]
    
    def get_full_domain_url(self, obj):
        # Construct full URL (http://<domain>/)
        if obj.domain:
            return f"http://{obj.domain}:8000/api/tenant/"
        return None