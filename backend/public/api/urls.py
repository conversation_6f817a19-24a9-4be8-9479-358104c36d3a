from django.urls import path, include
from public.api.views import RegisterRestaurantView
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from public.api.views import PublicTenantViewSet
from .views import BillingPlanViewSet

urlpatterns = [
    path('restaurants/', PublicTenantViewSet.as_view({'get': 'list'}), name='public-restaurants'),
    path('billing-plans/', BillingPlanViewSet.as_view({'get': 'list'}), name='billing-plans'),
    path('register-restaurant/', RegisterRestaurantView.as_view(), name='restaurant-register'),
    path('customer/', include('users.api.urls')),
]