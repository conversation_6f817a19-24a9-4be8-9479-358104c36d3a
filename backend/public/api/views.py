from rest_framework.generics import CreateAPIView
from tenants.models import PendingClient
from .serializers import PendingClientSerializer, BillingPlanSerializer
from rest_framework.permissions import AllowAny
from django.db.models import Count, Q
from rest_framework.viewsets import ReadOnlyModelViewSet
from tenants.models import Client, Domain
from .serializers import PublicTenantSerializer
from django.db.models import Count, Q, Subquery, OuterRef
from billing.models import BillingPlan

class RegisterRestaurantView(CreateAPIView):
    queryset = PendingClient.objects.all()
    serializer_class = PendingClientSerializer
    permission_classes = [AllowAny]

class PublicTenantViewSet(ReadOnlyModelViewSet):
    permission_classes = [AllowAny]
    serializer_class = PublicTenantSerializer

    def get_queryset(self):
        # Subquery to get the primary domain for each tenant
        domain_subquery = Domain.objects.filter(
            tenant=OuterRef('pk'),
            is_primary=True
        ).values('domain')[:1]

        return Client.objects.filter(
            active=True
        ).exclude(
            restaurant_name="Public Tenant"
        ).annotate(
            favorite_count=Count(
                'favorited_by',
                filter=Q(favorited_by__role='customer')
            ),
            domain=Subquery(domain_subquery)
        )

class BillingPlanViewSet(ReadOnlyModelViewSet):
    permission_classes = [AllowAny]
    queryset = BillingPlan.objects.all()
    serializer_class = BillingPlanSerializer