from django.contrib import admin, messages
from django.core.mail import send_mail
from django.contrib.auth.hashers import make_password
from django_tenants.utils import schema_context
from .models import Client, PendingClient, Domain
from django.contrib.auth import get_user_model
import secrets
import traceback
from billing.models import BillingPlanChoice
from django.contrib.admin.utils import get_deleted_objects
from django.core.validators import validate_email
from django.db import router
import logging
from .models import PendingEmailClient

logger = logging.getLogger(__name__)
User = get_user_model()

@admin.register(PendingEmailClient)
class PendingEmailClientAdmin(admin.ModelAdmin):
    list_display = ('client', 'created_at')
    actions = ['resend_email']
    
    def resend_email(self, request, queryset):
        success_count = 0
        failures = []
        
        for pending in queryset:
            client = pending.client
            try:
                send_mail(
                    'Your Restaurant Has Been Approved',
                    f'Login URL: http://{pending.domain_name}:8000/api/tenant/\nUsername: {pending.username}\nPassword: {pending.password}',
                    '<EMAIL>',
                    [client.manager_email],
                    fail_silently=False,
                )
                pending.delete()  # Remove after successful send
                success_count += 1
            except Exception as e:
                failures.append(f"{client.restaurant_name}: {str(e)}")
        
        if success_count:
            self.message_user(
                request, 
                f"Successfully resent {success_count} email(s)", 
                messages.SUCCESS
            )
        for failure in failures:
            self.message_user(request, failure, level=messages.ERROR)
    
    resend_email.short_description = "Resend approval email to selected clients"

@admin.action(description="Approve selected restaurants")
def approve_restaurants(modeladmin, request, queryset):
    for pending in queryset:
        paid_until = pending.trial_end_date if pending.billing_plan == BillingPlanChoice.FREE_TRIAL else None

        client = Client.objects.create(
            restaurant_name=pending.restaurant_name,
            schema_name=pending.schema_name,
            manager_email=pending.manager_email,
            manager_firstname=pending.manager_firstname,
            manager_lastname=pending.manager_lastname,
            manager_phone=pending.manager_phone,
            phone_number=pending.restaurant_phone,
            address=pending.address,
            location=pending.location,
            description=pending.description,
            logo=pending.logo,
            banner=pending.banner,
            billing_plan=pending.billing_plan,
            paid_until=paid_until,
            active=True,
        )

        domain_name = f"{client.schema_name.replace('_','-')}.localhost"
        domain = Domain.objects.create(
            domain=domain_name,
            tenant=client,
            is_primary=True,
            is_active=True
        )
        
        username = f"manager@{domain_name}"
        password = secrets.token_urlsafe(12)

        with schema_context(client.schema_name):
            user = User.objects.create_user(
                username=username,
                email=pending.manager_email,
                password=password,
                first_name=pending.manager_firstname,
                last_name=pending.manager_lastname,
                is_active=True,
                role='manager',
                restaurant=client
            )
            user.is_staff = True  # Required for admin access
            user.save()

        validate_email(pending.manager_email)

        try:
            send_mail(
                'Your Restaurant Has Been Approved',
                f'Login URL: http://{domain_name}:8000/api/tenant/\nUsername: {username}\nPassword: {password}',
                '<EMAIL>',
                [pending.manager_email],
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f"Email failed: {e}\n{traceback.format_exc()}")
            # Store only domain and username, not password
            PendingEmailClient.objects.create(
                client=client,
                domain_name=domain_name,
                username=username
            )
            modeladmin.message_user(
                request,
                f"Approved {pending.restaurant_name} but failed to send email. Credentials saved for resending.",
                level=messages.WARNING
            )
        
        pending.approved = True
        pending.save()

@admin.register(PendingClient)
class PendingClientAdmin(admin.ModelAdmin):
    list_display = ('restaurant_name', 'schema_name', 'manager_email', 'approved', 'created_on')
    list_filter = ('approved',)
    actions = [approve_restaurants]

    def get_queryset(self, request):
        return super().get_queryset(request).filter(approved=False)
    
    actions = [approve_restaurants, 'delete_selected']
    
    def delete_selected(self, request, queryset):
        """Simple bulk delete without confirmation"""
        count = queryset.count()
        queryset.delete()
        self.message_user(request, f"Deleted {count} pending restaurants")
    delete_selected.short_description = "Delete selected pending restaurants"

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('restaurant_name', 'billing_plan', 'active', 'on_trial', 'paid_until')
    list_filter = ('billing_plan', 'active', 'on_trial')
    search_fields = ('restaurant_name',)
    actions = ['activate_tenants', 'deactivate_tenants']

    def get_deleted_objects(self, objs, request):
        """
        Override to prevent queries to tenant schemas during deletion confirmation
        """
        model_name = self.model._meta.verbose_name_plural
        warning = "WARNING: Deleting a tenant will permanently destroy all restaurant data!"
        return (
            [str(obj) for obj in objs] + [warning],
            {model_name: len(objs)},
            set(),
            set()
        )

    def delete_queryset(self, request, queryset):
        """Handle bulk deletion with proper messaging"""
        success_count = 0
        errors = []
        
        # Prevent default admin deletion messages
        queryset.model._meta.auto_created = True
        
        for obj in queryset:
            try:
                obj.delete()
                success_count += 1
            except Exception as e:
                errors.append(f"{obj.restaurant_name}: {str(e)}")
        
        # Show only one summary message
        if success_count:
            self.message_user(
                request, 
                f"Successfully deleted {success_count} client(s)", 
                messages.SUCCESS
            )
        for error in errors:
            self.message_user(request, error, level=messages.ERROR)

    def delete_model(self, request, obj):
        """Handle single object deletion"""
        # Prevent default admin deletion message
        obj._meta.auto_created = True
        
        try:
            obj.delete()
        except Exception as e:
            self.message_user(request, f"Error deleting restaurant: {e}", level=messages.ERROR)
    
    def activate_tenants(self, request, queryset):
        queryset.update(active=True)
    activate_tenants.short_description = "Activate selected tenants"

    def deactivate_tenants(self, request, queryset):
        queryset.update(active=False)
    deactivate_tenants.short_description = "Deactivate selected tenants"

@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    list_display = ('domain', 'tenant', 'is_primary')
    list_filter = ('is_primary',)
    search_fields = ('domain', 'tenant__restaurant_name')