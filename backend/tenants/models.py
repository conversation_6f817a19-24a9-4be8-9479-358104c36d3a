from django.db import models, connection, transaction
from django_tenants.models import TenantMixin, DomainMixin
from django.utils.translation import gettext_lazy as _
from django.contrib.gis.db import models as gis_models 
from datetime import timedelta
from django.utils import timezone
from backend.settings import DEFAULT_TRIAL_PERIOD
from django_tenants.utils import schema_exists, schema_context
import logging
from django.db.models.signals import pre_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

class BillingPlanOption(models.TextChoices):
    FREE_TRIAL = 'free_trial', _('Free Trial')
    BASIC = 'basic', _('Basic')
    STANDARD = 'standard', _('Standard')
    PREMIUM = 'premium', _('Premium')

class Client(TenantMixin):
    restaurant_name = models.CharField(max_length=100)
    schema_name = models.CharField(max_length=63, unique=True)
    paid_until = models.DateField(null=True, blank=True)
    on_trial = models.BooleanField(default=True)
    created_on = models.DateField(auto_now_add=True)
    billing_plan = models.CharField(
        max_length=20,
        choices=BillingPlanOption.choices,
        default=BillingPlanOption.FREE_TRIAL
    )

    # Manager details
    manager_firstname = models.CharField(max_length=100)
    manager_lastname = models.CharField(max_length=100)
    manager_phone = models.CharField(max_length=100)
    manager_email = models.EmailField(unique=True)

    # Restaurant Details
    phone_number = models.CharField(max_length=20)
    address = models.TextField()
    location = gis_models.PointField(
        geography=True,
        srid=4326,
        null=True,
        blank=True
    )
    description = models.TextField()
    logo = models.ImageField(upload_to='tenant_logos/', null=True, blank=True)
    banner = models.ImageField(upload_to='tenant_banners/', null=True, blank=True)
    active = models.BooleanField(default=False)  # Will be set to True on approval

    # Branding fields
    primary_color = models.CharField(max_length=7, default='#FF0000', blank=True, null=True)
    secondary_color = models.CharField(max_length=7, default='#00FF00', blank=True, null=True)

    is_deleting = models.BooleanField(default=False)

    auto_schema_create = True
    auto_drop_schema = True

    class Meta:
        indexes = [
            models.Index(fields=['id', 'is_deleting']),
            models.Index(fields=['schema_name']),
        ]

    def __str__(self):
        return self.restaurant_name
    
    def save(self, *args, **kwargs):
        if self.billing_plan == BillingPlanOption.FREE_TRIAL:
            self.on_trial = True
            if not self.paid_until:
                self.paid_until = timezone.now() + timedelta(days=DEFAULT_TRIAL_PERIOD)
        else:
            self.on_trial = False  # paid_until remains null until payment

        if self.active and not self._state.adding:
            Domain.objects.filter(tenant=self, is_primary=True).update(is_active=True)
            
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.is_deleting:
            return
        self.is_deleting = True
        self.save(update_fields=['is_deleting'])
        
        if connection.schema_name != 'public':
            raise Exception("Deletion must be done from public schema")

        try:
            schema_name = self.schema_name
            client_id = self.id
            
            # 1. Clean up public schema references
            User = get_user_model()
            
            # Remove from favorite restaurants (public schema)
            User.favorite_restaurants.through.objects.filter(client_id=client_id).delete()
            
            # 2. Clean up tenant schema references
            with schema_context(schema_name):
                # Disconnect tenant users
                User.objects.filter(restaurant_id=client_id).update(restaurant=None)
            
            # 3. Drop tenant schema
            if schema_exists(schema_name):
                with connection.cursor() as cursor:
                    cursor.execute(f'DROP SCHEMA IF EXISTS {schema_name} CASCADE')
            
            # 4. Delete domains and client in public schema
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Delete domains
                    cursor.execute(
                        "DELETE FROM tenants_domain WHERE tenant_id = %s",
                        [client_id]
                    )
                    # Delete client
                    cursor.execute(
                        "DELETE FROM tenants_client WHERE id = %s",
                        [client_id]
                    )
            
            # 5. Explicitly commit
            transaction.commit()
            
        except Exception as e:
            transaction.rollback()
            if Client.objects.filter(id=client_id).exists():
                Client.objects.filter(id=client_id).update(is_deleting=False)
            logger.error(f"Error deleting tenant {client_id}: {e}")
            raise

class Domain(DomainMixin):
    is_active = models.BooleanField(default=False)

    class Meta:
        db_table = 'tenants_domain'
        indexes = [
            models.Index(fields=['tenant']),
        ]
        
    pass

class PendingClient(models.Model):
    restaurant_name = models.CharField(max_length=100)
    schema_name = models.CharField(max_length=63, unique=True)
    manager_email = models.EmailField()
    manager_firstname = models.CharField(max_length=100)
    manager_lastname = models.CharField(max_length=100)
    manager_phone = models.CharField(max_length=100)
    restaurant_phone = models.CharField(max_length=100)
    location = gis_models.PointField(
        geography=True,
        srid=4326,
        null=True,
        blank=True
    )
    description = models.TextField(blank=True)
    address = models.TextField()
    logo = models.ImageField(upload_to='tenant_logos/', blank=True, null=True)
    banner = models.ImageField(upload_to='tenant_banners/', blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    approved = models.BooleanField(default=False)
    active = models.BooleanField(default=False)
    trial_end_date = models.DateField(null=True, blank=True)
    billing_plan = models.CharField(
        max_length=20,
        choices=BillingPlanOption.choices,
        default=BillingPlanOption.FREE_TRIAL
    )

    class Meta:
        verbose_name = _('Pending Restaurant')
        verbose_name_plural = _('Pending Restaurants')

    def __str__(self):
        return self.restaurant_name
    

    def save(self, *args, **kwargs):
        """Generate unique schema_name and set trial end date"""
        if not self.schema_name:
            base_schema = slugify(self.restaurant_name).replace('-', '_')[:63].lower()
            self.schema_name = base_schema
            counter = 1
            while PendingClient.objects.filter(schema_name=self.schema_name).exists() or \
                Client.objects.filter(schema_name=self.schema_name).exists():
                self.schema_name = f"{base_schema}_{counter}"
                counter += 1
        
        # Set trial end date for FREE_TRIAL plan
        if self.billing_plan == BillingPlanOption.FREE_TRIAL and not self.trial_end_date:
            self.trial_end_date = timezone.now() + timedelta(days=DEFAULT_TRIAL_PERIOD)
            
        super().save(*args, **kwargs)

class PendingEmailClient(models.Model):
    client = models.OneToOneField(
        Client,
        on_delete=models.CASCADE,
        related_name='pending_email'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    password = models.CharField(max_length=128) 
    domain_name = models.CharField(max_length=255)  
    username = models.CharField(max_length=255)  

    class Meta:
        verbose_name = _('Pending Email Client')
        verbose_name_plural = _('Pending Email Clients')

    def __str__(self):
        return f"Pending email for {self.client.restaurant_name}"
    
@receiver(pre_delete, sender=Client)
def delete_pending_email(sender, instance, **kwargs):
    if hasattr(instance, 'pending_email'):
        instance.pending_email.delete()