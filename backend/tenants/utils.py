from django.utils import timezone
from django_tenants.utils import tenant_context

def check_trial_expiration():
    from tenants.models import Restaurant
    expired = Restaurant.objects.filter(
        trial_end_date__lt=timezone.now().date(),
        tier='trial'
    )
    
    for tenant in expired:
        with tenant_context(tenant):
            # Downgrade to basic tier
            tenant.tier = 'basic'
            tenant.save()
            # Notify restaurant manager
            manager = tenant.staff.filter(role=UserRole.MANAGER).first()
            if manager:
                send_trial_expired_email(manager.email)
                
def tenant_from_request(request):
    """
    Retrieves the current tenant from the request object
    """
    # For public schema (superadmin access)
    if hasattr(request, 'tenant') and request.tenant:
        return request.tenant
    
    # Fallback to connection tenant (for background processes)
    if hasattr(connection, 'tenant'):
        return connection.tenant
    
    # Last resort: Get from hostname
    hostname = request.get_host().split(':')[0]
    return Client.objects.get(domain_url=hostname)