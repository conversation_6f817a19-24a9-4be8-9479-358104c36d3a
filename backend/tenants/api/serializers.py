from rest_framework import serializers
from users.models import User, User<PERSON>ole
import random

class StaffUserSerializer(serializers.ModelSerializer):
    role = serializers.ChoiceField(choices=[UserRole.WAITER, UserRole.DELIVERY])

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number', 'role', 'username']  # Added 'username'
        read_only_fields = ['username', 'password']  # 'username' is read-only
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        tenant = self.context['request'].tenant
        role = validated_data['role']
        
        # Generate unique username: <EMAIL>
        base_username = f"{role}@{tenant.domain}"
        username = base_username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{role}{counter}@{tenant.domain}"
            counter += 1

        # Generate random password
        password = User.objects.make_random_password()
        
        # Create staff user
        user = User.objects.create_user(
            username=username,
            password=password,
            role=role,
            restaurant=tenant,
            **validated_data
        )
        
        # Temporarily store password to include in response
        user.temp_password = password
        return user

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        # Include generated password only during creation
        if hasattr(instance, 'temp_password'):
            rep['password'] = instance.temp_password
        return rep