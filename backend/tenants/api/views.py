from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework import status
from rest_framework.response import Response
from tenants.models import Client
from users.models import UserRole
import jwt
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
from django_tenants.utils import get_tenant

class CrossDomainLoginView(APIView):
    """Exchange public token for tenant-specific JWT"""
    def post(self, request):
        token = request.data.get('token')
        if not token:
            return Response({'error': 'Token required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Verify token
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            current_tenant = get_tenant()
            
            # Validate tenant match
            if payload['tenant_id'] != current_tenant.id:
                return Response({'error': 'Invalid tenant'}, status=status.HTTP_403_FORBIDDEN)
            
            # Get user from public schema (shared model)
            user = User.objects.get(id=payload['user_id'])
            
            # Generate tenant-specific JWT
            refresh = RefreshToken.for_user(user)
            return Response({
                'access': str(refresh.access_token),
                'refresh': str(refresh),
            })
            
        except jwt.ExpiredSignatureError:
            return Response({'error': 'Token expired'}, status=status.HTTP_401_UNAUTHORIZED)
        except (jwt.InvalidTokenError, User.DoesNotExist):
            return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)

class TenantTokenObtainPairView(TokenObtainPairView):
    def post(self, request, *args, **kwargs):
        hostname = request.get_host().split(':')[0]
        try:
            tenant = Client.objects.get(domains__domain=hostname)
        except Client.DoesNotExist:
            return Response(
                {"error": "Tenant not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Correct way to access authenticated user
        user = serializer.user
        
        # Authorization checks
        if user.role == UserRole.SUPERUSER:
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        
        # Staff must belong to current tenant
        if user.role in [UserRole.MANAGER, UserRole.WAITER, UserRole.DELIVERY]:
            if user.restaurant_id != tenant.id:
                return Response(
                    {"detail": "Not authorized for this restaurant"},
                    status=status.HTTP_403_FORBIDDEN
                )
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        
        return Response(
            {"detail": "Invalid credentials for restaurant access"},
            status=status.HTTP_403_FORBIDDEN
        )