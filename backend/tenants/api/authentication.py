from rest_framework.authentication import TokenAuthentication
from django_tenants.utils import tenant_context
from tenants.models import Client
from rest_framework import exceptions

class TenantTokenAuthentication(TokenAuthentication):
    keyword = 'Bearer'

    def authenticate(self, request):
        hostname = request.get_host().split(':')[0]
        try:
            tenant = Client.objects.get(domains__domain=hostname)
        except Client.DoesNotExist:
            return None

        with tenant_context(tenant):
            try:
                return super().authenticate(request)
            except exceptions.AuthenticationFailed:
                return None