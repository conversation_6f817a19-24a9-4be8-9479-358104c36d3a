from django.contrib.auth.backends import ModelBackend
from django_tenants.utils import tenant_context
from .models import Client


class TenantBackend(ModelBackend):
    def authenticate(self, request, **kwargs):
        hostname = request.get_host().split(':')[0]
        try:
            tenant = Client.objects.get(domains__domain=hostname)
        except Client.DoesNotExist:
            return None
            
        with tenant_context(tenant):
            return super().authenticate(request, **kwargs)