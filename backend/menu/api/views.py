from rest_framework import viewsets, permissions
from menu.models import Category, MenuItem
from menu.api.serializers import CategorySerializer, MenuItemSerializer
from backend.permissions import IsManagerOrReadOnly

from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.permissions import AllowAny

class PublicMenuViewSet(ReadOnlyModelViewSet):
    permission_classes = [AllowAny]
    serializer_class = MenuItemSerializer
    
    def get_queryset(self):
        return MenuItem.objects.filter(
            is_available=True,
            restaurant__active=True  # Only show items from active restaurants
        ).select_related('category').order_by('category__order', 'name')

class CategoryViewSet(viewsets.ModelViewSet):
    serializer_class = CategorySerializer
    permission_classes = [IsManagerOrReadOnly]
    
    def get_queryset(self):
        # Automatically filters by current tenant
        return Category.objects.filter(
            restaurant=self.request.tenant,
            is_active=True
        ).order_by('order')
    
    def perform_create(self, serializer):
        # Automatically set restaurant to current tenant
        serializer.save(restaurant=self.request.tenant)
           
    def perform_update(self, serializer):
        # Prevent changing restaurant after creation
        serializer.save(restaurant=self.request.tenant)
        
class MenuItemViewSet(viewsets.ModelViewSet):
    serializer_class = MenuItemSerializer
    permission_classes = [IsManagerOrReadOnly]
    filterset_fields = ['category', 'is_available', 'is_vegetarian', 'is_vegan']
    
    def get_queryset(self):
        # Automatically filters by current tenant
        return MenuItem.objects.filter(
            restaurant=self.request.tenant,
            is_available=True
        ).select_related('category')
    
    def perform_create(self, serializer):
        # Set restaurant to current tenant and ensure category belongs to tenant
        category = serializer.validated_data.get('category')
        if category and category.restaurant != self.request.tenant:
            raise PermissionDenied("Invalid category selected")
        serializer.save(restaurant=self.request.tenant)
        
    def perform_update(self, serializer):
        # Prevent changing restaurant and validate category
        instance = self.get_object()
        serializer.save(restaurant=instance.restaurant)