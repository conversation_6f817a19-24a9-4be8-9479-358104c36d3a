from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from users.api.views import StaffViewSet
from tenants.api.views import TenantTokenObtainPairView
from rest_framework_simplejwt.views import TokenRefreshView
from restaurant.api.views import (
    PaymentViewSet,
    NotificationViewSet,
    RestaurantConfigurationView,
    RestaurantHomeView,
    RestaurantThemeView,
    ThemeOptionsView,
)
from tenants.api.views import CrossDomainLoginView
from orders.api.views import GuestOrderCreateView, GuestOrderStatusView

router = DefaultRouter()
router.register(r'staff', StaffViewSet, basename='staff')

urlpatterns = [
    path('', RestaurantHomeView.as_view(), name='restaurant-home'),
    path('login/', TenantTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('payments/', PaymentViewSet.as_view({'get': 'list'})),
    path('notifications/', NotificationViewSet.as_view({'get': 'list'})),
    path('config/', RestaurantConfigurationView.as_view(), name='restaurant-configuration'),
    path('staff/', StaffViewSet.as_view({'get': 'list', 'post': 'create'}), name='staff-list'),
    path('staff/<int:pk>/', StaffViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='staff-detail'),
    path('menu/', include('menu.api.urls')),
    path('orders/', include('orders.api.urls')),
    path('auth/exchange/', CrossDomainLoginView.as_view(), name='cross-domain-login'),
    path('theme/', RestaurantThemeView.as_view(), name='restaurant-theme'),
    path('theme-options/', ThemeOptionsView.as_view(), name='theme-options'),
] + router.urls