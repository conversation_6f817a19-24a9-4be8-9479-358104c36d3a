from django.db import connection
from django.http import Http404
from django.conf import settings
from tenants.models import Client  # your tenant model

class TenantMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        hostname = request.get_host().split(':')[0]  # remove port
        tenant_subdomain = hostname.split('.')[0]  # get tenant part

        try:
            tenant = Tenant.objects.get(domain_url__startswith=tenant_subdomain)
        except Tenant.DoesNotExist:
            raise Http404("Tenant not found")

        request.tenant = tenant
        connection.set_schema(tenant.schema_name)  # switch schema

        # Set tenant-specific URLconf
        request.urlconf = settings.TENANT_URLCONF

        response = self.get_response(request)

        # Optionally reset schema to public after response
        connection.set_schema_to_public()

        return response