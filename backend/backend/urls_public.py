from django.urls import path, include
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static
from public.api.views import PublicTenantViewSet

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/public/', include('public.api.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)