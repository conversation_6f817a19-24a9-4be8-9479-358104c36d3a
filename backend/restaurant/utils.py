from django.core.mail import send_mail
from  django.template.loader import render_to_string
from django.utils.html import strip_tags
from core.settings import DEFAULT_FROM_EMAIL

def send_notification_email(notificaion):
    subject = notificaion.title
    html_message = render_to_string('emails/notification.html', {
        'notification': notificaion
    })
    plain_message = strip_tags(html_message)

    send_mail(
        subject=subject,
        message=plain_message,
        from_email=DEFAULT_FROM_EMAIL,
        recipient_list=[notification.recipient.email],
        html_message=html_message,
        fail_silently=False,
    )

    def send_meal_ready_nofitication(order):
        subject = f"Your meal from {order.restaurant.name} is ready!"
        html_message = render_to_string('emails/meal_ready.html', {
            'order': order
        })
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=DEFAULT_FROM_EMAIL,
            recipient_list=[order.customer.email],
            html_message=html_message,
            fail_silently=False,
        )