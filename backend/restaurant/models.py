from django.db import models
from django_tenants.models import TenantMixin
from django.utils.translation import gettext_lazy as _ 

class Payment(models.Model):
    tenant = models.ForeignKey(
        'tenants.Client',
        on_delete=models.DO_NOTHING,
        related_name='+',
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateTimeField(auto_now_add=True)
    payment_method = models.CharField(max_length=50)
    transaction_id = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['date']
    
    def __str__(self):
        return f"Payment #{self.id} - {self.tenant.name}"

class Notification(models.Model):
    tenant = models.ForeignKey(
        'tenants.Client',
        on_delete=models.DO_NOTHING,
        related_name='+',
    )
    recipient = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    title = models.CharField(max_length=200)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Notification to {self.recipient} from {self.tenant.name}"

class RestaurantTheme(models.Model):
    restaurant = models.OneToOneField(
        'tenants.Client',
        on_delete=models.DO_NOTHING,
        related_name='theme',
    )
    primary_color = models.CharField(max_length=7, default='#FF6600')
    secondary_color = models.CharField(max_length=7, default='#FFFFFF')
    accent_color = models.CharField(max_length=7, default='#FFD700')
    font_family = models.CharField(max_length=50, default='Roboto')
    rounded_corners = models.BooleanField(default=True)
    dark_mode = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Theme for {self.restaurant.name}"

class RestaurantConfiguration(models.Model):
    restaurant = models.OneToOneField(
        'tenants.Client',
        on_delete=models.DO_NOTHING,
        related_name='configuration',
    )
    enable_online_payments = models.BooleanField(default=True)
    enable_cash_on_delivery = models.BooleanField(default=True)
    delivery_radius_km = models.PositiveIntegerField(default=10)
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    order_confirmation_sms = models.BooleanField(default=True)
    order_confirmation_email = models.BooleanField(default=True)
    opening_time = models.TimeField(blank=True, null=True)
    closing_time = models.TimeField(blank=True, null=True)
    #opening_hours = models.JSONField(blank=True, null=True)

    def __str__(self):
        return f"Configuration for {self.restaurant.name}"