from rest_framework import viewsets, permissions, generics, status
from restaurant.models import Payment, Notification, RestaurantConfiguration, RestaurantTheme
from django_tenants.utils import tenant_context, schema_context, get_public_schema_name
from tenants.models import Client
from rest_framework.views import APIView
from rest_framework.response import Response
from django.http import Http404
from django.utils import timezone
from menu.models import Category, MenuItem
from restaurant.api.serializers import (
    PaymentSerializer,
    NotificationSerializer,
    RestaurantConfigurationSerializer,
    RestaurantSerializer,
    RestaurantThemeSerializer
)
from django.core.cache import cache
from backend.permissions import IsRestaurantManager
from restaurant import theme_options

class RestaurantThemeView(generics.RetrieveUpdateAPIView):
    serializer_class = RestaurantConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]

    def get_object(self):
        with tenant_context(self.request.tenant):
            obj, created = RestaurantTheme.objects.get_or_create(
                restaurant=self.request.tenant
            )
            return obj

class RestaurantConfigurationView(generics.RetrieveUpdateAPIView):
    serializer_class = RestaurantConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]

    def get_object(self):
        with tenant_context(self.request.tenant):
            obj, created = RestaurantConfiguration.objects.get_or_create(
                restaurant=self.request.tenant
            )
            return obj

class ThemeOptionsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        return Response({
            "colors": theme_options.COLORS,
            "fonts": theme_options.FONTS
        })

class PaymentViewSet(viewsets.ModelViewSet):
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Payment.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return Payment.objects.filter(tenant=self.request.tenant)
    
class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]
    queryset = Notification.objects.none()

    def get_queryset(self):
        with tenant_context(self.request.tenant):
            return Notification.objects.filter(recipient=self.request.user)

    def perform_create(self, serializer):
        serializer.save(tenant=self.request.tenant, recipient=self.request.user)

class RestaurantDetailView(generics.RetrieveAPIView):
    queryset = Client.objects.all()
    serializer_class = RestaurantSerializer

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        restaurant = self.get_object()
        context['favorite_count'] = restaurant.favorited_by.count()
        return context

class RestaurantHomeView(APIView):
    """
    Public view for restaurant/tenant home details
    """
    permission_classes = [permissions.AllowAny]

    def get(self, request, *args, **kwargs):
        # Get current tenant from request
        tenant = request.tenant
        
        # Check if restaurant is active
        if not tenant.active:
            return Response(
                {"error": "Restaurant is not active"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Prepare restaurant details
        restaurant_data = {
            "name": tenant.restaurant_name,
            "description": tenant.description,
            "logo": request.build_absolute_uri(tenant.logo.url) if tenant.logo else None,
            "address": tenant.address,
            "location": {
                "latitude": tenant.location.y if tenant.location else None,
                "longitude": tenant.location.x if tenant.location else None
            },
            "phone_number": tenant.phone_number,
            "banner": request.build_absolute_uri(tenant.banner.url) if tenant.banner else None,
        }
        
        # Get theme details
        try:
            # theme = tenant.theme
            theme = RestaurantTheme.objects.get(restaurant=tenant)
            restaurant_data.update({
                "primary_color": theme.primary_color,
                "secondary_color": theme.secondary_color,
                "font_family": theme.font_family,
                "rounded_corners": theme.rounded_corners,
                "dark_mode": theme.dark_mode,
            })
        except RestaurantTheme.DoesNotExist:
            # Fallback to tenant colors if theme doesn't exist
            restaurant_data.update({
                "primary_color": tenant.primary_color,
                "secondary_color": tenant.secondary_color,
                "font_family": "Roboto",  # Default
                "rounded_corners": True,   # Default
                "dark_mode": False,        # Default
            })
        
        # Get configuration details
        try:
            # config = tenant.configuration
            config = RestaurantConfiguration.objects.get(restaurant=tenant)
            restaurant_data.update({
                "enable_online_payments": config.enable_online_payments,
                "enable_cash_on_delivery": config.enable_cash_on_delivery,
                "delivery_radius_km": config.delivery_radius_km,
                "minimum_order_amount": float(config.minimum_order_amount),
                "delivery_fee": float(config.delivery_fee),
                "opening_time": config.opening_time.strftime("%H:%M") if config.opening_time else None,
                "closing_time": config.closing_time.strftime("%H:%M") if config.closing_time else None,
            })
        except RestaurantConfiguration.DoesNotExist:
            # Provide default configuration if not set
            restaurant_data.update({
                "enable_online_payments": True,
                "enable_cash_on_delivery": True,
                "delivery_radius_km": 10,
                "minimum_order_amount": 0.0,
                "delivery_fee": 0.0,
                "opening_time": None,
                "closing_time": None,
            })
        
        # Prepare menu structure
        menu = []
        categories = Category.objects.filter(
            restaurant=tenant, 
            is_active=True
        ).order_by('order')
        
        for category in categories:
            items = MenuItem.objects.filter(
                category=category, 
                is_available=True
            ).order_by('name')
            
            category_data = {
                "id": category.id,
                "name": category.name,
                "description": category.description,
                "items": []
            }
            
            for item in items:
                item_data = {
                    "id": item.id,
                    "name": item.name,
                    "description": item.description,
                    "price": float(item.price),
                    "image": request.build_absolute_uri(item.image.url) if item.image else None,
                    "preparation_time": item.preparation_time,
                    "dietary_info": {
                        "is_vegetarian": item.is_vegetarian,
                        "is_vegan": item.is_vegan,
                        "is_glutten_free": item.is_glutten_free,
                    },
                    "calories": item.calories
                }
                category_data["items"].append(item_data)
            
            menu.append(category_data)
        
        # Add favorite count from public schema
        cache_key = f"restaurant_{tenant.id}_favorite_count"
        favorite_count = cache.get(cache_key)

        if favorite_count is None:
            with schema_context(get_public_schema_name()):
                try:
                    restaurant_in_public = Client.objects.get(id=tenant.id)
                    favorite_count = restaurant_in_public.favorited_by.count()
                except Client.DoesNotExist:
                    favorite_count = 0
            cache.set(cache_key, favorite_count, 300)  # Cache 5 minutes

        restaurant_data["favorite_count"] = favorite_count
        
        response_data = {
            "restaurant": restaurant_data,
            "menu": menu,
            "current_time": timezone.localtime().strftime("%H:%M"),
            "is_open": self.check_if_open(
                restaurant_data.get("opening_time"),
                restaurant_data.get("closing_time")
            )
        }
        
        return Response(response_data)
        
    def check_if_open(self, opening_time, closing_time):
        """Check if restaurant is currently open"""
        if not opening_time or not closing_time:
            return None
        
        current_time = timezone.localtime().time()
        opening = timezone.datetime.strptime(opening_time, "%H:%M").time()
        closing = timezone.datetime.strptime(closing_time, "%H:%M").time()
        
        # Handle overnight hours
        if closing < opening:
            return current_time >= opening or current_time <= closing
        return opening <= current_time <= closing