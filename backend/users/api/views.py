from rest_framework import viewsets, permissions, status, generics, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from users.models import User, UserRole
from tenants.models import Client, Domain
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .serializers import CustomerTokenObtainSerializer
from django.db.models import Count, Q, Subquery, OuterRef
import jwt
from django.conf import settings
from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from datetime import datetime, timedelta
from users.api.serializers import (
    UserSerializer, 
    StaffUserSerializer,
    CustomerRegistrationSerializer, 
    FavoriteRestaurantSerializer,
    PublicTenantSerializer
)
from backend.permissions import (
    IsRestaurantManager,
    IsRegisteredCustomer,
    IsPublicTenant
)

class CustomerAuthTokenView(TokenObtainPairView):
    serializer_class = CustomerTokenObtainSerializer

class CustomerTokenRefreshView(TokenRefreshView):
    pass

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # For restaurant managers, only show their staff
        if self.request.user.is_manager:
            return User.objects.filter(restaurant=self.request.user.restaurant)
        # For superusers, show all users
        elif self.request.user.is_superuser:
            return super().get_queryset()
        # For others, only show their own profile
        return User.objects.get(id=self.request.user.id)

    def get_permissions(self):
        if self.action == 'register':
            return [permissions.AllowAny]
        return super().get_permisions()

    @action(detail=False, methods=['post'])
    def register(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        refresh = RefreshToken.for_user(user)
        return Response({
            "user": serializer.data,
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            }, status=status.HTTP_201_CREATED
        )
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

class StaffViewSet(viewsets.ModelViewSet):
    serializer_class = StaffUserSerializer
    permission_classes = [permissions.IsAuthenticated, IsRestaurantManager]
    queryset = User.objects.all()

    def get_queryset(self):
        # Return only waiter/delivery staff for current tenant
        return User.objects.filter(
            restaurant=self.request.tenant,
            role__in=[UserRole.WAITER, UserRole.DELIVERY]
        )

    def perform_create(self, serializer):
        serializer.save()

    def perform_destroy(self, instance):
        # Soft-delete instead of permanent deletion
        instance.is_active = False
        instance.save()

class CustomerRegistrationView(generics.CreateAPIView):
    serializer_class = CustomerRegistrationSerializer
    permission_classes = [IsPublicTenant]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            return Response(
                {'error': 'Validation failed', 'details': e.detail},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            self.perform_create(serializer)
        except Exception as e:
            return Response(
                {'error': 'User creation failed', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
        return Response(
            {'message': 'Customer registered successfully'}, 
            status=status.HTTP_201_CREATED
        )

class FavoriteRestaurantView(generics.GenericAPIView):
    serializer_class = FavoriteRestaurantSerializer
    permission_classes = [permissions.IsAuthenticated, IsRegisteredCustomer]
    
    # Get favorite restaurants list
    def get(self, request, *args, **kwargs):
        user = request.user
        favorite_restaurants = user.favorite_restaurants.all()
        
        # Serialize restaurant data (ID and domain)
        favorites_data = [{
            'id': r.id,
            'domain': r.domains.first().domain  # Access primary domain
        } for r in favorite_restaurants]
        
        return Response(favorites_data, status=status.HTTP_200_OK)
    
    # Add restaurant to favorites
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        restaurant_id = serializer.validated_data['restaurant_id']
        try:
            restaurant = Client.objects.get(id=restaurant_id)
        except Client.DoesNotExist:
            return Response(
                {'error': 'Restaurant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        user = request.user
        if user.favorite_restaurants.filter(id=restaurant.id).exists():
            return Response(
                {'status': 'Restaurant already in favorites', 'restaurant_id': restaurant.id},
                status=status.HTTP_200_OK
            )
        
        user.favorite_restaurants.add(restaurant)
        return Response(
            {
                'status': 'Restaurant added to favorites',
                'restaurant_id': restaurant.id
            },
            status=status.HTTP_201_CREATED
        )
    
    # Remove restaurant from favorites
    def delete(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        restaurant_id = serializer.validated_data['restaurant_id']
        try:
            restaurant = Client.objects.get(id=restaurant_id)
        except Client.DoesNotExist:
            return Response(
                {'error': 'Restaurant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        user = request.user
        if not user.favorite_restaurants.filter(id=restaurant.id).exists():
            return Response(
                {'error': 'Restaurant not in favorites', 'restaurant_id': restaurant.id},
                status=status.HTTP_404_NOT_FOUND
            )
        
        user.favorite_restaurants.remove(restaurant)
        return Response(
            {
                'status': 'Restaurant removed from favorites',
                'restaurant_id': restaurant.id
            },
            status=status.HTTP_200_OK
        )

class AllRestaurantsViewSet(ReadOnlyModelViewSet):
    permission_classes = [permissions.IsAuthenticated, IsRegisteredCustomer]
    serializer_class = PublicTenantSerializer

    def get_queryset(self):
        # Subquery to get the primary domain for each tenant
        domain_subquery = Domain.objects.filter(
            tenant=OuterRef('pk'),
            is_primary=True
        ).values('domain')[:1]

        return Client.objects.filter(
            active=True
        ).exclude(
            restaurant_name="Public Tenant"
        ).annotate(
            favorite_count=Count(
                'favorited_by',
                filter=Q(favorited_by__role='customer')
            ),
            domain=Subquery(domain_subquery)  # Annotate domain
        )
    
class TenantAuthTokenView(APIView):
    """Generate cross-domain authentication token for specific tenant"""
    permission_classes = [permissions.IsAuthenticated, IsRegisteredCustomer]

    def get(self, request, tenant_id):
        # Get target tenant and domain
        tenant = get_object_or_404(Client, id=tenant_id, active=True)
        domain = Domain.objects.get(tenant=tenant, is_primary=True)
        
        # Create JWT token
        token_payload = {
            'user_id': request.user.id,
            'tenant_id': tenant.id,
            'domain': domain.domain,
            'exp': datetime.utcnow() + timedelta(minutes=5)
        }
        token = jwt.encode(token_payload, settings.SECRET_KEY, algorithm='HS256')
        
        return Response({
            'token': token,
            'redirect_url': f'http://{domain.domain}:8000/api/tenant/auth/exchange/'
        })