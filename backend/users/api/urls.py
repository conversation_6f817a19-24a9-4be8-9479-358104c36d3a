from django.urls import path
from .views import (
    CustomerRegistrationView,
    FavoriteRestaurantView,
    CustomerAuthTokenView,
    CustomerTokenRefreshView,
    AllRestaurantsViewSet,
    TenantAuthTokenView
)
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('restaurants/', AllRestaurantsViewSet.as_view({'get': 'list'}), name='all_restaurants'),
    path('register/', CustomerRegistrationView.as_view(), name='customer_register'),
    path('favorite-restaurants/', FavoriteRestaurantView.as_view(), name='favorite_restaurants'),
    path('login/', TokenObtainPairView.as_view(), name='customer_login'),
    path('token/refresh/', CustomerTokenRefreshView.as_view(), name='token_refresh'),
    path('tenant-token/<int:tenant_id>/', TenantAuthTokenView.as_view(), name='tenant-auth-token'),
]