# Django Multitenant Authentication Flow Documentation

## Overview

This Django application implements a sophisticated multitenant architecture where restaurants operate as separate tenants with their own schemas, while maintaining a shared public schema for common functionality.

## Architecture Components

### 1. Tenant Models
- **Client**: Represents approved restaurants (tenants)
- **PendingClient**: Represents restaurants awaiting approval
- **Domain**: Maps domains to tenants
- **User**: Custom user model with role-based access

### 2. User Roles
```python
class UserRole(models.TextChoices):
    SUPERUSER = 'superuser'    # Platform administrators
    MANAGER = 'manager'        # Restaurant managers
    WAITER = 'waiter'         # Restaurant staff
    DELIVERY = 'delivery'     # Delivery personnel
    CUSTOMER = 'customer'     # End customers
```

## Authentication Flows

### 1. Restaurant Registration & Approval Flow

#### Step 1: Restaurant Registration
- **Endpoint**: `POST /api/public/register-restaurant/`
- **Process**:
  1. Restaurant owner submits registration form
  2. Creates `PendingClient` record with restaurant details
  3. Generates unique `schema_name` based on restaurant name
  4. Status: `approved=False`

#### Step 2: Admin Approval
- **Location**: Django Admin (`/admin/`)
- **Process**:
  1. Platform admin reviews `PendingClient` entries
  2. Uses `approve_restaurants` admin action
  3. Creates:
     - `Client` record (tenant)
     - `Domain` record (subdomain mapping)
     - Manager user account in tenant schema
  4. Sends approval email with login credentials

#### Step 3: Tenant Creation
```python
# Creates tenant with schema
client = Client.objects.create(
    restaurant_name=pending.restaurant_name,
    schema_name=pending.schema_name,
    # ... other fields
    active=True
)

# Creates subdomain mapping
domain = Domain.objects.create(
    domain=f"{schema_name}.localhost",
    tenant=client,
    is_primary=True,
    is_active=True
)

# Creates manager user in tenant schema
with schema_context(client.schema_name):
    user = User.objects.create_user(
        username=f"manager@{domain_name}",
        email=pending.manager_email,
        role='manager',
        restaurant=client,
        is_staff=True  # Admin access
    )
```

### 2. Restaurant Manager Login Flow

#### Admin Interface Login
- **URL**: `http://{restaurant-subdomain}.localhost:8000/admin/`
- **Credentials**: 
  - Username: `manager@{restaurant-subdomain}.localhost`
  - Password: Auto-generated (sent via email)
- **Access**: Full Django admin for their tenant schema

#### API Login
- **Endpoint**: `POST /api/auth/login/`
- **Authentication**: Token-based
- **Process**:
  1. Manager submits credentials
  2. `TenantAuthTokenView` validates against tenant schema
  3. Returns authentication token
  4. Token used for subsequent API calls

### 3. Customer Authentication Flow

#### Registration
- **Endpoint**: `POST /api/public/customer/register/`
- **Schema**: Public schema
- **Role**: `customer`

#### Login
- **Endpoint**: `POST /api/public/customer/login/`
- **Authentication**: JWT tokens
- **Access**: Public API endpoints

### 4. Staff Management

Restaurant managers can create staff accounts:
- **Endpoint**: `POST /api/auth/staff/`
- **Roles**: `waiter`, `delivery`
- **Schema**: Tenant-specific
- **Access**: Limited to restaurant operations

## Authentication Backends

### 1. TenantBackend
```python
class TenantBackend(ModelBackend):
    def authenticate(self, request, **kwargs):
        hostname = request.get_host().split(':')[0]
        tenant = Client.objects.get(domains__domain=hostname)
        
        with tenant_context(tenant):
            return super().authenticate(request, **kwargs)
```

### 2. TenantTokenAuthentication
```python
class TenantTokenAuthentication(TokenAuthentication):
    def authenticate(self, request):
        hostname = request.get_host().split(':')[0]
        tenant = Client.objects.get(domains__domain=hostname)
        
        with tenant_context(tenant):
            return super().authenticate(request)
```

## URL Routing

### Public Schema URLs (`core/urls_public.py`)
- `/admin/` - Platform admin
- `/api/public/` - Public API endpoints
- `/api/tenant/` - Tenant management

### Tenant Schema URLs (`core/urls_tenants.py`)
- `/` - Tenant home
- `/api/auth/` - Tenant authentication
- `/api/payments/` - Payment management
- `/api/config/` - Restaurant configuration

## Permission System

### Custom Permissions
- `IsRestaurantManager`: Manager access to their restaurant
- `IsRegisteredCustomer`: Customer-specific access
- `IsPublicTenant`: Public schema access

### Role-based Access
```python
def save(self, *args, **kwargs):
    self.is_staff = self.role in [UserRole.SUPERUSER, UserRole.MANAGER]
    self.is_superuser = self.role == UserRole.SUPERUSER
```

## Security Features

1. **Schema Isolation**: Each restaurant operates in separate database schema
2. **Domain-based Routing**: Automatic tenant detection via subdomain
3. **Role-based Permissions**: Granular access control
4. **Token Authentication**: Secure API access
5. **Email Verification**: Approval workflow with email notifications

## Login Examples

### Platform Admin
- URL: `http://localhost:8000/admin/`
- User: Superuser account
- Access: All tenants and platform management

### Restaurant Manager
- URL: `http://restaurant-name.localhost:8000/admin/`
- User: `<EMAIL>`
- Access: Restaurant-specific admin and API

### API Authentication
```bash
# Get token
curl -X POST http://restaurant-name.localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password"}'

# Use token
curl -H "Authorization: Bearer <token>" \
  http://restaurant-name.localhost:8000/api/config/
```

## Database Schema

- **Public Schema**: Shared data (tenants, domains, public users)
- **Tenant Schemas**: Restaurant-specific data (menus, orders, staff)
- **User Association**: Users linked to tenants via `restaurant` field
