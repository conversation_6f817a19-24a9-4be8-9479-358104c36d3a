# EasyEats Web App Backend Integration Work Plan

## Overview

Many pages in the EasyEats web app currently use dummy/static data. To complete the project and deliver a fully functional product, we need to replace all mock data with real backend API calls, handle loading/error states, and ensure all CRUD operations are wired up for each user role (customer, manager, admin).

---

## 1. Audit: Pages Using Dummy Data

### Landing Page (`src/app/page.js`)

- Featured restaurants, categories, and stats are hardcoded.
- Needs: Fetch featured restaurants, categories, and stats from backend.

### Customer Dashboard (`src/app/customer/page.jsx`)

- User info, recent orders, and recommendations are hardcoded.
- Needs: Fetch user profile, order history, and recommendations from backend.

### Customer Restaurants List & Details (`src/app/customer/restaurants/page.jsx`, `[id]/page.jsx`)

- Restaurant lists, details, dishes, reviews, and staff are hardcoded.
- Needs: Fetch all restaurant data, dishes, reviews, and staff from backend.

### Customer Order History (`src/app/customer/history/page.jsx`)

- Order and restaurant data are mocked.
- Needs: Fetch order history and restaurant info from backend.

### Manager Dashboard (`src/app/manager/dashboard/home/<USER>

- Stats and recent activity are hardcoded.
- Needs: Fetch stats and activity from backend.

### Manager Restaurants List & Details (`src/app/manager/dashboard/restaurants/page.jsx`, `[id]/page.jsx`)

- Restaurant list, details, dishes, and users are hardcoded.
- Needs: Fetch all data from backend, enable CRUD for dishes and users.

### Manager Users (`src/app/manager/dashboard/users/page.jsx`)

- User list is hardcoded.
- Needs: Fetch users from backend, enable user management.

### Admin Dashboard & Users (`src/app/admin/dashboard/home/<USER>/page.jsx`, `requests/page.jsx`)

- User and request lists are hardcoded.
- Needs: Fetch users and registration requests from backend, enable approval/rejection.

---

## 2. Work Plan & Timeline

### Week 1 (Aug 28 - Sep 3)

- [ ] **Landing Page**: Replace all static data with API calls for featured restaurants, categories, and stats.
- [ ] **Customer Dashboard**: Fetch user profile, recent orders, and recommendations from backend.
- [ ] **Set up API utility**: Create a reusable API/fetch utility for all requests.

### Week 2 (Sep 4 - Sep 10)

- [ ] **Customer Restaurants**: Fetch restaurant list, details, dishes, reviews, and staff from backend.
- [ ] **Customer Order History**: Fetch order history and related restaurant info from backend.
- [ ] **Loading/Error States**: Add loading and error UI for all customer-facing pages.

### Week 3 (Sep 11 - Sep 17)

- [ ] **Manager Dashboard**: Fetch stats and recent activity from backend.
- [ ] **Manager Restaurants**: Fetch list/details, enable CRUD for restaurants, dishes, and users.
- [ ] **Manager Users**: Fetch and manage users from backend.

### Week 4 (Sep 18 - Sep 24)

- [ ] **Admin Dashboard**: Fetch users and registration requests from backend.
- [ ] **Admin Actions**: Enable approval/rejection of requests, user management.
- [ ] **Testing**: End-to-end testing of all flows.

### Week 5 (Sep 25 - Sep 30)

- [ ] **Polish & QA**: Final bug fixes, UI polish, and documentation.
- [ ] **Deployment**: Prepare for production deployment.

---

## 3. General Steps for Each Page

1. Identify all static/dummy data.
2. Replace with async fetch/API calls to backend endpoints.
3. Handle loading and error states.
4. Refactor UI to use real data.
5. Test all CRUD operations (where applicable).

---

## 4. Risks & Dependencies

- Backend API must be stable and documented.
- Some endpoints may need to be created/updated.
- Team communication is key for smooth integration.

---

## 5. Success Criteria

- No page uses dummy data.
- All data is fetched from backend and updates in real time.
- All CRUD operations work for each user role.
- App is ready for production by end of September 2025.

---

_Last updated: August 28, 2025_
