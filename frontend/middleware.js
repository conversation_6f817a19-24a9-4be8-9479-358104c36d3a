// middleware.ts
import { NextRequest, NextResponse } from "next/server";

const ROOT_DOMAIN = process.env.PUBLIC_ROOT_DOMAIN || "lvh.me";
const TENANT_HEADER = "x-tenant";

function hostToTenant(host) {
  if (!host) return null;
  const [hostname] = host.split(":");
  const hostWithoutPort = hostname.toLowerCase();

  // localhost/127.* -> path mode or no tenant
  if (hostWithoutPort === "localhost" || hostWithoutPort.startsWith("127.")) {
    return null;
  }
  if (hostWithoutPort === ROOT_DOMAIN) return null;

  const dotRoot = `.${ROOT_DOMAIN}`;
  if (hostWithoutPort.endsWith(dotRoot)) {
    const sub = hostWithoutPort.slice(0, -dotRoot.length);
    if (!sub || sub === "www") return null;
    return sub;
  }
  return null;
}

export function middleware(req) {
  const url = req.nextUrl.clone();
  const { pathname } = url;
  const method = req.method;
  const host = req.headers.get("host") || "";

  // 0) Skip API & Next internals entirely
  if (
    pathname.startsWith("/api") ||
    pathname.startsWith("/_next") ||
    pathname === "/favicon.ico" ||
    pathname === "/robots.txt" ||
    pathname === "/sitemap.xml"
  ) {
    return NextResponse.next();
  }

  // 1) Never interfere with preflight or non-navigation requests
  // (middleware is only for page navigations)
  const fetchMode = req.headers.get("sec-fetch-mode"); // "navigate", "cors", ...
  const fetchDest = req.headers.get("sec-fetch-dest"); // "document", "empty", ...
  if (
    method === "OPTIONS" ||
    fetchMode !== "navigate" ||
    fetchDest !== "document"
  ) {
    return NextResponse.next();
  }

  // 2) Tenant logic only for real page navigations
  const tenant = hostToTenant(host);

  if (!tenant) {
    // No tenant: keep users on landing "/"
    if (pathname !== "/") {
      url.pathname = "/";
      // For page navs, rewrite keeps origin same (no CORS/redirect issues)
      return NextResponse.rewrite(url);
    }
    return NextResponse.next();
  }

  // Tenant present: force the tenant homepage route
  if (!pathname.startsWith("/restaurants/restaurant")) {
    url.pathname = "/restaurants/restaurant";
    // Prefer rewrite for smooth UX; use redirect if you truly need a new URL
    const res = NextResponse.rewrite(url);
    // Optionally set a header/cookie for server components to read
    res.headers.set(TENANT_HEADER, tenant);
    res.cookies.set("tenant", tenant, { path: "/", sameSite: "lax" });
    return res;
  }

  const res = NextResponse.next();
  res.headers.set(TENANT_HEADER, tenant);
  res.cookies.set("tenant", tenant, { path: "/", sameSite: "lax" });
  return res;
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|api|favicon.ico).*)"],
};
