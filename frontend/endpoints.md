# Easy Eats API Endpoints Documentation

This document provides a comprehensive overview of all API endpoints in the Easy Eats backend system.

## Base URLs

The system uses Django Tenants with different URL patterns:

- **Public Domain**: For customer registration and restaurant registration
- **Tenant Domains**: For restaurant-specific operations (each restaurant has its own subdomain)

## Authentication

The system uses multiple authentication methods:

- **Token Authentication**: For manager authentication
- **JWT Authentication**: For customer authentication
- **Session Authentication**: For admin operations

---

## Public API Endpoints

### Restaurant Registration

**Endpoint**: `POST /api/public/register-restaurant/`
**Description**: Register a new restaurant (creates a pending client)
**Authentication**: None required
**Input**:

```json
{
  "restaurant_name": "string (required)",
  "manager_email": "email (required)",
  "manager_firstname": "string (required)",
  "manager_lastname": "string (required)",
  "manager_phone": "string (required)",
  "restaurant_phone": "string (required)",
  "longitude": "float (required)",
  "latitude": "float (required)",
  "description": "string (optional)",
  "address": "string (required)",
  "logo": "image file (optional)",
  "banner": "image file (optional)"
}
```

**Output**:

```json
{
  "id": "integer",
  "restaurant_name": "string",
  "schema_name": "string (auto-generated)",
  "manager_email": "email",
  "manager_firstname": "string",
  "manager_lastname": "string",
  "manager_phone": "string",
  "restaurant_phone": "string",
  "longitude": "float",
  "latitude": "float",
  "description": "string",
  "address": "string",
  "logo": "url",
  "banner": "url"
}
```

### Manager Authentication

**Endpoint**: `POST /api/public/manager-auth/`
**Description**: Authenticate restaurant managers using restaurant_id
**Authentication**: None required
**Input**:

```json
{
  "restaurant_id": "string (required) - schema name of restaurant",
  "username": "string (required)",
  "password": "string (required)"
}
```

**Output**:

```json
{
  "token": "string",
  "user_id": "integer",
  "email": "string",
  "restaurant_name": "string",
  "schema_name": "string"
}
```

**Error Responses**:

- `400`: Missing required fields
- `404`: Restaurant not found
- `401`: Invalid credentials or user is not a manager

### Customer Registration

**Endpoint**: `POST /api/public/customer/register/`
**Description**: Register a new customer
**Authentication**: None required
**Input**:

```json
{
  "email": "email (required)",
  "username": "string (required)",
  "password": "string (required)",
  "confirm_password": "string (required)",
  "phone_number": "string (optional)"
}
```

**Output**:

```json
{
  "message": "Customer registered successfully"
}
```

**Error Responses**:

- `400`: Validation errors (password mismatch, existing email/username)

### Customer Login

**Endpoint**: `POST /api/public/customer/login/`
**Description**: Customer authentication using JWT
**Authentication**: None required
**Input**:

```json
{
  "username": "string (required)",
  "password": "string (required)"
}
```

**Output**:

```json
{
  "access": "string (JWT access token)",
  "refresh": "string (JWT refresh token)",
  "user": {
    "id": "integer",
    "email": "string",
    "role": "string",
    "username": "string",
    "phone_number": "string"
  }
}
```

### Customer Token Refresh

**Endpoint**: `POST /api/public/customer/token/refresh/`
**Description**: Refresh JWT access token
**Authentication**: None required
**Input**:

```json
{
  "refresh": "string (required) - JWT refresh token"
}
```

**Output**:

```json
{
  "access": "string (new JWT access token)"
}
```

### Customer Favorite Restaurants

**Endpoint**: `POST /api/public/customer/favorite-restaurants/`
**Description**: Add or remove restaurant from customer favorites
**Authentication**: JWT required (customer role)
**Input**:

```json
{
  "restaurant_id": "integer (required)"
}
```

**Output**:

```json
{
  "status": "string (Restaurant added/removed from favorites)",
  "restaurant_id": "integer",
  "is_favorited": "boolean"
}
```

**Error Responses**:

- `404`: Restaurant not found

---

## Tenant API Endpoints

These endpoints are available on tenant-specific domains (e.g., restaurant1.example.com)

### Tenant Authentication

**Endpoint**: `POST /api/auth/login/`
**Description**: Login for tenant users (staff, managers)
**Authentication**: None required
**Input**:

```json
{
  "username": "string (required)",
  "password": "string (required)"
}
```

**Output**:

```json
{
  "token": "string",
  "user_id": "integer",
  "email": "string"
}
```

### Staff Management

#### List/Create Staff

**Endpoint**: `GET/POST /api/auth/staff/`
**Description**: List all staff or create new staff member
**Authentication**: Token required (manager role)
**Methods**: GET, POST

**GET Output**:

```json
[
  {
    "id": "integer",
    "first_name": "string",
    "last_name": "string",
    "email": "string",
    "phone_number": "string",
    "role": "string (waiter/delivery)",
    "username": "string"
  }
]
```

**POST Input**:

```json
{
  "first_name": "string (required)",
  "last_name": "string (required)",
  "email": "string (required)",
  "phone_number": "string (required)",
  "role": "string (required) - waiter or delivery"
}
```

**POST Output**:

```json
{
  "id": "integer",
  "first_name": "string",
  "last_name": "string",
  "email": "string",
  "phone_number": "string",
  "role": "string",
  "username": "string (auto-generated)",
  "password": "string (auto-generated)"
}
```

#### Staff Detail Operations

**Endpoint**: `GET/PUT/PATCH/DELETE /api/auth/staff/{id}/`
**Description**: Retrieve, update, or delete specific staff member
**Authentication**: Token required (manager role)
**Methods**: GET, PUT, PATCH, DELETE

**GET Output**: Same as individual staff object above

**PUT/PATCH Input**: Same as POST input (all fields for PUT, partial for PATCH)

**DELETE**: Soft delete (sets is_active=False)

### Menu Management

#### Categories

##### List/Create Categories

**Endpoint**: `GET/POST /api/auth/menu/categories/`
**Description**: List all categories or create new category
**Authentication**: Token required (read-only for all, write for managers)
**Methods**: GET, POST

**GET Output**:

```json
[
  {
    "id": "integer",
    "name": "string",
    "description": "string",
    "is_active": "boolean",
    "order": "integer"
  }
]
```

**POST Input**:

```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "is_active": "boolean (default: true)",
  "order": "integer (default: 0)"
}
```

##### Category Detail Operations

**Endpoint**: `GET/PUT/PATCH/DELETE /api/auth/menu/categories/{id}/`
**Description**: Retrieve, update, or delete specific category
**Authentication**: Token required (read-only for all, write for managers)
**Methods**: GET, PUT, PATCH, DELETE

#### Menu Items

##### List/Create Menu Items

**Endpoint**: `GET/POST /api/auth/menu/menu-items/`
**Description**: List all menu items or create new menu item
**Authentication**: Token required (read-only for all, write for managers)
**Methods**: GET, POST
**Query Parameters**:

- `category`: Filter by category ID
- `is_available`: Filter by availability
- `is_vegetarian`: Filter vegetarian items
- `is_vegan`: Filter vegan items

**GET Output**:

```json
[
  {
    "id": "integer",
    "name": "string",
    "description": "string",
    "price": "decimal",
    "image": "url",
    "preparation_time": "integer (minutes)",
    "is_available": "boolean",
    "is_vegetarian": "boolean",
    "is_vegan": "boolean",
    "calories": "integer",
    "is_glutten_free": "boolean",
    "category": {
      "id": "integer",
      "name": "string",
      "description": "string",
      "is_active": "boolean",
      "order": "integer"
    }
  }
]
```

**POST Input**:

```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "price": "decimal (required)",
  "image": "image file (optional)",
  "preparation_time": "integer (required)",
  "is_available": "boolean (default: true)",
  "is_vegetarian": "boolean (default: false)",
  "is_vegan": "boolean (default: false)",
  "calories": "integer (optional)",
  "is_glutten_free": "boolean (default: false)",
  "category_id": "integer (required)"
}
```

##### Menu Item Detail Operations

**Endpoint**: `GET/PUT/PATCH/DELETE /api/auth/menu/menu-items/{id}/`
**Description**: Retrieve, update, or delete specific menu item
**Authentication**: Token required (read-only for all, write for managers)
**Methods**: GET, PUT, PATCH, DELETE

### Order Management

#### List/Create Orders

**Endpoint**: `GET/POST /api/auth/orders/orders/`
**Description**: List orders or create new order
**Authentication**: Token required
**Methods**: GET, POST
**Query Parameters**:

- `status`: Filter by order status
- `payment_method`: Filter by payment method
- `is_paid`: Filter by payment status

**GET Output** (customers see only their orders, staff see all):

```json
[
  {
    "id": "integer",
    "customer": {
      "id": "integer",
      "username": "string",
      "email": "string",
      "phone_number": "string"
    },
    "status": "string (pending/confirmed/preparing/on_delivery/delivered/cancelled)",
    "created_at": "datetime",
    "updated_at": "datetime",
    "total_amount": "decimal",
    "payment_method": "string (cash/card/online)",
    "is_paid": "boolean",
    "delivery_address": "string",
    "delivery_instructions": "string",
    "delivery_person": {
      "id": "integer",
      "username": "string",
      "email": "string"
    },
    "items": [
      {
        "id": "integer",
        "menu_item": "integer",
        "quantity": "integer",
        "price": "decimal",
        "special_instructions": "string"
      }
    ],
    "payment": {
      "id": "integer",
      "amount": "decimal",
      "transaction_id": "string",
      "payment_method": "string",
      "status": "string",
      "created_at": "datetime"
    }
  }
]
```

**POST Input**:

```json
{
  "payment_method": "string (required) - cash/card/online",
  "delivery_address": "string (required)",
  "delivery_instructions": "string (optional)",
  "items": [
    {
      "menu_item": "integer (required)",
      "quantity": "integer (required)",
      "price": "decimal (required)",
      "special_instructions": "string (optional)"
    }
  ]
}
```

#### Order Detail Operations

**Endpoint**: `GET/PUT/PATCH/DELETE /api/auth/orders/orders/{id}/`
**Description**: Retrieve, update, or delete specific order
**Authentication**: Token required
**Methods**: GET, PUT, PATCH, DELETE

### Payment Transactions

#### List/Create Payment Transactions

**Endpoint**: `GET/POST /api/auth/orders/payments/`
**Description**: List payment transactions or create new transaction
**Authentication**: Token required
**Methods**: GET, POST

**GET Output**:

```json
[
  {
    "id": "integer",
    "amount": "decimal",
    "transaction_id": "string",
    "payment_method": "string",
    "status": "string",
    "created_at": "datetime"
  }
]
```

#### Payment Transaction Detail Operations

**Endpoint**: `GET/PUT/PATCH/DELETE /api/auth/orders/payments/{id}/`
**Description**: Retrieve, update, or delete specific payment transaction
**Authentication**: Token required
**Methods**: GET, PUT, PATCH, DELETE

### Restaurant Management

#### Payments

**Endpoint**: `GET /api/auth/payments/`
**Description**: List restaurant payments
**Authentication**: Token required
**Methods**: GET

**Output**:

```json
[
  {
    "id": "integer",
    "amount": "decimal",
    "date": "datetime",
    "payment_method": "string",
    "transaction_id": "string",
    "description": "string"
  }
]
```

#### Notifications

**Endpoint**: `GET /api/auth/notifications/`
**Description**: List user notifications
**Authentication**: Token required
**Methods**: GET

**Output**:

```json
[
  {
    "id": "integer",
    "title": "string",
    "message": "string",
    "created_at": "datetime",
    "is_read": "boolean"
  }
]
```

#### Restaurant Configuration

**Endpoint**: `GET/PUT /api/auth/config/`
**Description**: Get or update restaurant configuration
**Authentication**: Token required (admin users only)
**Methods**: GET, PUT

**GET Output**:

```json
{
  "enable_online_payment": "boolean",
  "enable_cash_on_delivery": "boolean",
  "delivery_radius_km": "integer",
  "minimum_order_amount": "decimal",
  "opening_time": "time",
  "closing_time": "time",
  "order_confirmation_email": "boolean",
  "order_confirmation_sms": "boolean",
  "delivery_fee": "decimal"
}
```

**PUT Input**: Same structure as GET output

---

## Error Responses

### Common HTTP Status Codes

- `200`: Success
- `201`: Created successfully
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `500`: Internal Server Error

### Error Response Format

```json
{
  "error": "string (error message)",
  "details": "string or object (additional error details)"
}
```

---

## Notes

1. **Tenant Context**: All tenant-specific endpoints automatically filter data by the current tenant (restaurant).

2. **Permissions**:

   - Public endpoints require no authentication
   - Customer endpoints require JWT authentication
   - Tenant endpoints require Token authentication
   - Manager-only endpoints require manager role
   - Admin-only endpoints require admin permissions

3. **Automatic Fields**: Some fields are automatically set:

   - `restaurant` field is set to current tenant
   - `customer` field is set to current user for orders
   - `created_at`, `updated_at` timestamps

4. **File Uploads**: Image fields accept multipart/form-data uploads.

5. **Filtering**: Many list endpoints support query parameter filtering as noted in individual endpoint descriptions.

6. **Pagination**: List endpoints may implement pagination (not explicitly shown in current implementation).

7. **ViewSet Endpoints**: Django REST Framework ViewSets automatically provide the following URL patterns:

   - `GET /resource/` - List all items
   - `POST /resource/` - Create new item
   - `GET /resource/{id}/` - Retrieve specific item
   - `PUT /resource/{id}/` - Update entire item
   - `PATCH /resource/{id}/` - Partial update of item
   - `DELETE /resource/{id}/` - Delete item

8. **Data Types**:
   - `string`: Text field
   - `integer`: Whole number
   - `decimal`: Decimal number with precision
   - `boolean`: true/false value
   - `datetime`: ISO 8601 formatted datetime
   - `time`: Time in HH:MM format
   - `email`: Valid email address
   - `url`: Valid URL
   - `image file`: Multipart file upload
