"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, MapPin, Star, Clock } from "lucide-react";
import apiService, { ApiError } from "@/lib/api";
import { toast } from "sonner";
import Image from "next/image";

export function RestaurantsList({ className = "" }) {
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [favoriteLoading, setFavoriteLoading] = useState({});

  useEffect(() => {
    fetchRestaurants();
  }, []);

  const fetchRestaurants = async () => {
    try {
      setLoading(true);
      const data = await apiService.getRestaurants();
      setRestaurants(data.results || data);
    } catch (error) {
      console.error("Error fetching restaurants:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to load restaurants: " + error.message);
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFavorite = async (restaurantId) => {
    if (!apiService.isAuthenticated("customer")) {
      toast.error("Please login to add favorites");
      return;
    }

    try {
      setFavoriteLoading((prev) => ({ ...prev, [restaurantId]: true }));

      await apiService.toggleFavoriteRestaurant(restaurantId);

      toast.success("Added to favorites!");

      // Refresh restaurants to update favorite count
      await fetchRestaurants();
    } catch (error) {
      console.error("Error toggling favorite:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to update favorite: " + error.message);
      } else {
        toast.error("Failed to update favorite");
      }
    } finally {
      setFavoriteLoading((prev) => ({ ...prev, [restaurantId]: false }));
    }
  };

  const handleVisitRestaurant = async (restaurant) => {
    try {
      if (apiService.isAuthenticated("customer")) {
        // Get cross-domain token for authenticated customers
        const tokenData = await apiService.getCustomerTenantToken(
          restaurant.id
        );
        window.open(tokenData.redirect_url, "_blank");
      } else {
        // Direct visit for non-authenticated users
        const domain =
          restaurant.domain || `restaurant${restaurant.id}.localhost:8000`;
        window.open(`http://${domain}`, "_blank");
      }
    } catch (error) {
      console.error("Error visiting restaurant:", error);
      // Fallback to direct visit
      const domain =
        restaurant.domain || `restaurant${restaurant.id}.localhost:8000`;
      window.open(`http://${domain}`, "_blank");
    }
  };

  if (loading) {
    return (
      <div className={`grid gap-6 md:grid-cols-2 lg:grid-cols-3 ${className}`}>
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="h-48 bg-gray-200 rounded-t-lg"></div>
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-4"></div>
              <div className="flex justify-between">
                <div className="h-8 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (restaurants.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">No restaurants found</h3>
        <p className="text-gray-600">Check back later for new restaurants!</p>
      </div>
    );
  }

  return (
    <div className={`grid gap-6 md:grid-cols-2 lg:grid-cols-3 ${className}`}>
      {restaurants.map((restaurant) => (
        <Card
          key={restaurant.id}
          className="overflow-hidden hover:shadow-lg transition-shadow"
        >
          <div className="relative h-48">
            {restaurant.banner ? (
              <Image
                src={restaurant.banner}
                alt={restaurant.restaurant_name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {restaurant.restaurant_name.charAt(0)}
                </span>
              </div>
            )}
            <div className="absolute top-2 right-2 flex gap-2">
              <Badge variant="secondary" className="bg-white/90">
                <Heart className="w-3 h-3 mr-1" />
                {restaurant.favorite_count || 0}
              </Badge>
            </div>
          </div>

          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-lg line-clamp-1">
                {restaurant.restaurant_name}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFavorite(restaurant.id)}
                disabled={favoriteLoading[restaurant.id]}
                className="p-1 h-auto"
              >
                <Heart
                  className={`w-4 h-4 ${
                    favoriteLoading[restaurant.id] ? "animate-pulse" : ""
                  }`}
                />
              </Button>
            </div>

            {restaurant.description && (
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {restaurant.description}
              </p>
            )}

            <div className="flex items-center gap-4 text-xs text-gray-500 mb-4">
              {restaurant.address && (
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  <span className="line-clamp-1">{restaurant.address}</span>
                </div>
              )}
            </div>

            <div className="flex justify-between items-center">
              <Button
                onClick={() => handleVisitRestaurant(restaurant)}
                className="flex-1 mr-2"
              >
                View Menu
              </Button>

              <Badge variant="outline" className="text-xs">
                <Clock className="w-3 h-3 mr-1" />
                Open
              </Badge>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default RestaurantsList;
