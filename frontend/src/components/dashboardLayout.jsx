import Navbar from "./ui/app-navbar";
import { AppSidebar } from "./ui/app-sidebar";
import { SidebarProvider } from "./ui/sidebar";

export default function DashboardLayout({ children, sidebarItems, role }) {
  return (
    <SidebarProvider>
      <AppSidebar items={sidebarItems} />
      <main className="flex flex-col w-full m-2">
        <Navbar role={role} />
        <div className="flex-1">{children}</div>
      </main>
    </SidebarProvider>
  );
}
