"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Save, X } from "lucide-react";
import apiService, { ApiError } from "@/lib/api";
import { toast } from "sonner";

export function MenuManagement({ restaurantId, className = "" }) {
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [newCategory, setNewCategory] = useState({ name: "", description: "" });
  const [showNewCategory, setShowNewCategory] = useState(false);

  useEffect(() => {
    if (restaurantId) {
      fetchMenuData();
    }
  }, [restaurantId]);

  const fetchMenuData = async () => {
    try {
      setLoading(true);
      const [categoriesData, itemsData] = await Promise.all([
        apiService.getMenuCategories(restaurantId),
        apiService.getMenuItems(restaurantId),
      ]);
      
      setCategories(categoriesData.results || categoriesData);
      setMenuItems(itemsData.results || itemsData);
    } catch (error) {
      console.error("Error fetching menu data:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to load menu: " + error.message);
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategory.name.trim()) {
      toast.error("Category name is required");
      return;
    }

    try {
      await apiService.createMenuCategory(restaurantId, newCategory);
      toast.success("Category created successfully!");
      setNewCategory({ name: "", description: "" });
      setShowNewCategory(false);
      await fetchMenuData();
    } catch (error) {
      console.error("Error creating category:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to create category: " + error.message);
      } else {
        toast.error("Failed to create category");
      }
    }
  };

  const handleCreateMenuItem = async (categoryId) => {
    const newItem = {
      name: "New Item",
      description: "",
      price: "0.00",
      category: categoryId,
      is_available: true,
    };

    try {
      await apiService.createMenuItem(restaurantId, newItem);
      toast.success("Menu item created successfully!");
      await fetchMenuData();
    } catch (error) {
      console.error("Error creating menu item:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to create menu item: " + error.message);
      } else {
        toast.error("Failed to create menu item");
      }
    }
  };

  const getCategoryItems = (categoryId) => {
    return menuItems.filter(item => item.category === categoryId);
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="mb-4">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Menu Management</h2>
        <Button
          onClick={() => setShowNewCategory(true)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Category
        </Button>
      </div>

      {/* New Category Form */}
      {showNewCategory && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Category</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                value={newCategory.name}
                onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter category name"
              />
            </div>
            <div>
              <Label htmlFor="categoryDescription">Description (Optional)</Label>
              <Textarea
                id="categoryDescription"
                value={newCategory.description}
                onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter category description"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleCreateCategory}>
                <Save className="w-4 h-4 mr-2" />
                Create Category
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowNewCategory(false);
                  setNewCategory({ name: "", description: "" });
                }}
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories and Menu Items */}
      <div className="space-y-4">
        {categories.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <h3 className="text-lg font-semibold mb-2">No categories found</h3>
              <p className="text-gray-600 mb-4">Create your first category to start building your menu.</p>
              <Button onClick={() => setShowNewCategory(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Category
              </Button>
            </CardContent>
          </Card>
        ) : (
          categories.map((category) => (
            <Card key={category.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {category.name}
                      <Badge variant="secondary">
                        {getCategoryItems(category.id).length} items
                      </Badge>
                    </CardTitle>
                    {category.description && (
                      <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                    )}
                  </div>
                  <Button
                    onClick={() => handleCreateMenuItem(category.id)}
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Plus className="w-3 h-3" />
                    Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {getCategoryItems(category.id).length === 0 ? (
                    <p className="text-gray-500 text-sm italic">No items in this category yet.</p>
                  ) : (
                    getCategoryItems(category.id).map((item) => (
                      <div
                        key={item.id}
                        className="flex justify-between items-center p-3 border rounded-lg"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          {item.description && (
                            <p className="text-sm text-gray-600">{item.description}</p>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <span className="font-semibold text-green-600">
                              ${parseFloat(item.price).toFixed(2)}
                            </span>
                            <Badge variant={item.is_available ? "default" : "secondary"}>
                              {item.is_available ? "Available" : "Unavailable"}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <Button size="sm" variant="outline">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

export default MenuManagement;
