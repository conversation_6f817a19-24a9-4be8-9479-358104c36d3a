"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  DollarSign,
} from "lucide-react";
import apiService, { ApiError } from "@/lib/api";
import { toast } from "sonner";
import Image from "next/image";

export function ShoppingCart({ restaurantId, className = "" }) {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState({});

  useEffect(() => {
    if (restaurantId) {
      fetchCart();
    }
  }, [restaurantId]);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const data = await apiService.getCart(restaurantId);
      setCart(data);
    } catch (error) {
      console.error("Error fetching cart:", error);
      if (error instanceof ApiError && error.status !== 404) {
        toast.error("Failed to load cart: " + error.message);
      }
      // If cart doesn't exist (404), that's normal for empty carts
      setCart(null);
    } finally {
      setLoading(false);
    }
  };

  const updateCartItemQuantity = async (itemId, newQuantity) => {
    if (newQuantity < 1) {
      await removeCartItem(itemId);
      return;
    }

    try {
      setUpdating(prev => ({ ...prev, [itemId]: true }));
      await apiService.updateCartItem(restaurantId, itemId, { quantity: newQuantity });
      await fetchCart();
      toast.success("Cart updated!");
    } catch (error) {
      console.error("Error updating cart item:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to update item: " + error.message);
      } else {
        toast.error("Failed to update item");
      }
    } finally {
      setUpdating(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const removeCartItem = async (itemId) => {
    try {
      setUpdating(prev => ({ ...prev, [itemId]: true }));
      await apiService.removeCartItem(restaurantId, itemId);
      await fetchCart();
      toast.success("Item removed from cart!");
    } catch (error) {
      console.error("Error removing cart item:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to remove item: " + error.message);
      } else {
        toast.error("Failed to remove item");
      }
    } finally {
      setUpdating(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const clearCart = async () => {
    if (!confirm("Are you sure you want to clear your cart?")) {
      return;
    }

    try {
      await apiService.clearCart(restaurantId);
      setCart(null);
      toast.success("Cart cleared!");
    } catch (error) {
      console.error("Error clearing cart:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to clear cart: " + error.message);
      } else {
        toast.error("Failed to clear cart");
      }
    }
  };

  const proceedToCheckout = async () => {
    if (!cart || cart.items.length === 0) {
      toast.error("Your cart is empty");
      return;
    }

    try {
      // Create order from cart
      const orderData = {
        payment_method: "cash_on_delivery", // Default payment method
        delivery_address: "", // This should come from a form
        special_instructions: "",
      };

      const order = await apiService.createOrder(restaurantId, orderData);
      toast.success("Order placed successfully!");
      
      // Clear cart after successful order
      setCart(null);
      
      // You might want to redirect to order confirmation page here
      console.log("Order created:", order);
    } catch (error) {
      console.error("Error creating order:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to place order: " + error.message);
      } else {
        toast.error("Failed to place order");
      }
    }
  };

  const calculateTotal = () => {
    if (!cart || !cart.items) return 0;
    return cart.items.reduce((total, item) => {
      return total + (item.quantity * parseFloat(item.menu_item.price));
    }, 0);
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card className="animate-pulse">
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex gap-4">
                  <div className="w-16 h-16 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <ShoppingCart className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Your cart is empty</h3>
          <p className="text-gray-600">Add some delicious items to get started!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            Your Cart ({cart.items.length} items)
          </CardTitle>
          <Button variant="outline" size="sm" onClick={clearCart}>
            <Trash2 className="w-4 h-4 mr-2" />
            Clear Cart
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-4">
          {cart.items.map((item) => (
            <div key={item.id} className="flex gap-4 p-4 border rounded-lg">
              <div className="w-16 h-16 relative">
                {item.menu_item.image ? (
                  <Image
                    src={item.menu_item.image}
                    alt={item.menu_item.name}
                    fill
                    className="object-cover rounded"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-gray-400 text-xs">No Image</span>
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <h4 className="font-medium">{item.menu_item.name}</h4>
                {item.menu_item.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {item.menu_item.description}
                  </p>
                )}
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                      disabled={updating[item.id]}
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                    <span className="w-8 text-center">{item.quantity}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateCartItemQuantity(item.id, item.quantity + 1)}
                      disabled={updating[item.id]}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-semibold">
                      ${(item.quantity * parseFloat(item.menu_item.price)).toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-600">
                      ${parseFloat(item.menu_item.price).toFixed(2)} each
                    </div>
                  </div>
                </div>
              </div>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => removeCartItem(item.id)}
                disabled={updating[item.id]}
                className="self-start"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          ))}
        </div>

        {/* Cart Summary */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <span className="text-lg font-semibold">Total:</span>
            <span className="text-xl font-bold text-green-600 flex items-center gap-1">
              <DollarSign className="w-5 h-5" />
              {calculateTotal().toFixed(2)}
            </span>
          </div>
          
          <Button 
            onClick={proceedToCheckout} 
            className="w-full flex items-center gap-2"
            size="lg"
          >
            <CreditCard className="w-4 h-4" />
            Proceed to Checkout
          </Button>
          
          <p className="text-xs text-gray-500 text-center mt-2">
            You can review your order before final confirmation
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

export default ShoppingCart;
