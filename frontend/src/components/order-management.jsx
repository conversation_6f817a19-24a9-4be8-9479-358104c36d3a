"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Clock,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  User,
  Phone,
  MapPin,
  DollarSign,
} from "lucide-react";
import apiService, { ApiError } from "@/lib/api";
import { toast } from "sonner";

export function OrderManagement({ restaurantId, className = "" }) {
  const [orders, setOrders] = useState([]);
  const [deliveryPersons, setDeliveryPersons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState("all");

  useEffect(() => {
    if (restaurantId) {
      fetchOrders();
      fetchDeliveryPersons();
    }
  }, [restaurantId, statusFilter]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const params = statusFilter !== "all" ? { status: statusFilter } : {};
      const data = await apiService.getOrders(restaurantId, params);
      setOrders(data.results || data);
    } catch (error) {
      console.error("Error fetching orders:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to load orders: " + error.message);
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchDeliveryPersons = async () => {
    try {
      const data = await apiService.getFreeDeliveryPersons(restaurantId);
      setDeliveryPersons(data);
    } catch (error) {
      console.error("Error fetching delivery persons:", error);
    }
  };

  const handleStatusUpdate = async (orderId, newStatus) => {
    try {
      await apiService.updateOrderStatus(restaurantId, orderId, newStatus);
      toast.success("Order status updated successfully!");
      await fetchOrders();
    } catch (error) {
      console.error("Error updating order status:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to update status: " + error.message);
      } else {
        toast.error("Failed to update status");
      }
    }
  };

  const handleAssignDelivery = async (orderId, deliveryPersonId) => {
    try {
      await apiService.assignDeliveryPerson(restaurantId, orderId, deliveryPersonId);
      toast.success("Delivery person assigned successfully!");
      await fetchOrders();
      await fetchDeliveryPersons();
    } catch (error) {
      console.error("Error assigning delivery person:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to assign delivery: " + error.message);
      } else {
        toast.error("Failed to assign delivery");
      }
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4" />;
      case "confirmed":
        return <Package className="w-4 h-4" />;
      case "preparing":
        return <Package className="w-4 h-4" />;
      case "ready":
        return <CheckCircle className="w-4 h-4" />;
      case "on_delivery":
        return <Truck className="w-4 h-4" />;
      case "delivered":
        return <CheckCircle className="w-4 h-4" />;
      case "cancelled":
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "confirmed":
        return "bg-blue-100 text-blue-800";
      case "preparing":
        return "bg-orange-100 text-orange-800";
      case "ready":
        return "bg-green-100 text-green-800";
      case "on_delivery":
        return "bg-purple-100 text-purple-800";
      case "delivered":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      pending: "confirmed",
      confirmed: "preparing",
      preparing: "ready",
      ready: "on_delivery",
      on_delivery: "delivered",
    };
    return statusFlow[currentStatus];
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="mb-4">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Package className="w-6 h-6" />
          Order Management
        </h2>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Orders</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="confirmed">Confirmed</SelectItem>
            <SelectItem value="preparing">Preparing</SelectItem>
            <SelectItem value="ready">Ready</SelectItem>
            <SelectItem value="on_delivery">On Delivery</SelectItem>
            <SelectItem value="delivered">Delivered</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {orders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">No orders found</h3>
              <p className="text-gray-600">
                {statusFilter === "all"
                  ? "No orders have been placed yet."
                  : `No orders with status "${statusFilter}" found.`}
              </p>
            </CardContent>
          </Card>
        ) : (
          orders.map((order) => (
            <Card key={order.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      Order #{order.id}
                      <Badge className={getStatusColor(order.status)}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1 capitalize">
                          {order.status.replace("_", " ")}
                        </span>
                      </Badge>
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      {new Date(order.created_at).toLocaleString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 text-lg font-semibold text-green-600">
                      <DollarSign className="w-4 h-4" />
                      {parseFloat(order.total_amount).toFixed(2)}
                    </div>
                    <p className="text-sm text-gray-600 capitalize">
                      {order.payment_method?.replace("_", " ")}
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Customer Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Customer Information
                    </h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      {order.customer ? (
                        <>
                          <p>{order.customer.first_name} {order.customer.last_name}</p>
                          <p className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            {order.customer.phone_number}
                          </p>
                        </>
                      ) : (
                        <>
                          <p>Guest Order</p>
                          <p className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            {order.guest_phone}
                          </p>
                        </>
                      )}
                      {order.delivery_address && (
                        <p className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {order.delivery_address}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Order Items */}
                  <div>
                    <h4 className="font-medium mb-2">Order Items</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      {order.items?.map((item, index) => (
                        <div key={index} className="flex justify-between">
                          <span>{item.quantity}x {item.menu_item.name}</span>
                          <span>${(item.quantity * parseFloat(item.menu_item.price)).toFixed(2)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4 border-t">
                  {getNextStatus(order.status) && order.status !== "delivered" && order.status !== "cancelled" && (
                    <Button
                      onClick={() => handleStatusUpdate(order.id, getNextStatus(order.status))}
                      size="sm"
                    >
                      Mark as {getNextStatus(order.status).replace("_", " ")}
                    </Button>
                  )}

                  {order.status === "ready" && !order.delivery_person && deliveryPersons.length > 0 && (
                    <Select onValueChange={(value) => handleAssignDelivery(order.id, value)}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Assign delivery person" />
                      </SelectTrigger>
                      <SelectContent>
                        {deliveryPersons.map((person) => (
                          <SelectItem key={person.id} value={person.id.toString()}>
                            {person.name} - {person.phone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}

                  {order.delivery_person && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Truck className="w-3 h-3" />
                      Assigned to: {order.delivery_person.name}
                    </Badge>
                  )}

                  {order.status !== "delivered" && order.status !== "cancelled" && (
                    <Button
                      variant="outline"
                      onClick={() => handleStatusUpdate(order.id, "cancelled")}
                      size="sm"
                    >
                      Cancel Order
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

export default OrderManagement;
