"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export function RegisterForm({ className, ...props }) {
  const register = (e) => {
    e.preventDefault();
    console.log("Submitting...");
    alert("Submitting...");

    /*
    fetch("http://localhost:8000/api/public/customer/register/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: "<EMAIL>",
        username: "newcustomer",
        password: "StrongPassword123!",
        confirm_password: "StrongPassword123!",
        phone_number: "+1234567890",
      }),
    })
      .then((response) => response.json())
      .then((data) => console.log(data))
      .catch((error) => console.error("Error:", error));

    */
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0">
        <CardContent>
          <form onSubmit={register} className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Glad to have you back!</h1>
                <p className="text-muted-foreground text-balance">
                  Login to your Easy Eats account
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="email">Phone Number</Label>
                <Input
                  id="uname"
                  type="phone"
                  placeholder="+************"
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="email">Username</Label>
                <span className="text-xs text-gray-500">
                  Create a unique username
                </span>

                <Input id="uname" type="text" placeholder="johnDoe" required />
              </div>
              {/* TODO: Check username if exists from backend */}

              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                </div>
                <Input
                  id="password"
                  type="password"
                  required
                  placeholder="*******"
                />
              </div>

              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Comfirm Password</Label>
                </div>
                <Input
                  id="comfirm"
                  type="password"
                  required
                  placeholder="*******"
                />
              </div>

              <Button type="submit" className="w-full">
                Register
              </Button>
              {/* <div className="grid grid-cols-3 gap-4">
                <div></div>
                <Button variant="outline" type="button" className="w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  {/* <span className="sr-only">Login with Google</span>
                </Button>
                <div></div>
              </div> */}
              <div className="ml-auto text-center text-sm">
                Already have an account?{" "}
                <a href="/auth/login" className="underline underline-offset-4">
                  Login
                </a>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
