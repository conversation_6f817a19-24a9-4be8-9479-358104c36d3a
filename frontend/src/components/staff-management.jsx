"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Edit, Trash2, Save, X, Users, Phone, Mail } from "lucide-react";
import apiService, { ApiError } from "@/lib/api";
import { toast } from "sonner";

export function StaffManagement({ restaurantId, className = "" }) {
  const [staff, setStaff] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewStaff, setShowNewStaff] = useState(false);
  const [editingStaff, setEditingStaff] = useState(null);
  const [newStaff, setNewStaff] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    role: "",
  });

  useEffect(() => {
    if (restaurantId) {
      fetchStaff();
    }
  }, [restaurantId]);

  const fetchStaff = async () => {
    try {
      setLoading(true);
      const data = await apiService.getStaff(restaurantId);
      setStaff(data.results || data);
    } catch (error) {
      console.error("Error fetching staff:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to load staff: " + error.message);
      } else {
        toast.error("Network error. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStaff = async () => {
    if (!newStaff.first_name.trim() || !newStaff.last_name.trim() || !newStaff.email.trim() || !newStaff.role) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const response = await apiService.createStaff(restaurantId, newStaff);
      toast.success(`Staff member created! Username: ${response.username}, Password: ${response.password}`);
      setNewStaff({
        first_name: "",
        last_name: "",
        email: "",
        phone_number: "",
        role: "",
      });
      setShowNewStaff(false);
      await fetchStaff();
    } catch (error) {
      console.error("Error creating staff:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to create staff: " + error.message);
      } else {
        toast.error("Failed to create staff");
      }
    }
  };

  const handleUpdateStaff = async (staffId, data) => {
    try {
      await apiService.updateStaff(restaurantId, staffId, data);
      toast.success("Staff member updated successfully!");
      setEditingStaff(null);
      await fetchStaff();
    } catch (error) {
      console.error("Error updating staff:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to update staff: " + error.message);
      } else {
        toast.error("Failed to update staff");
      }
    }
  };

  const handleDeleteStaff = async (staffId, staffName) => {
    if (!confirm(`Are you sure you want to delete ${staffName}? This action cannot be undone.`)) {
      return;
    }

    try {
      await apiService.deleteStaff(restaurantId, staffId);
      toast.success("Staff member deleted successfully!");
      await fetchStaff();
    } catch (error) {
      console.error("Error deleting staff:", error);
      if (error instanceof ApiError) {
        toast.error("Failed to delete staff: " + error.message);
      } else {
        toast.error("Failed to delete staff");
      }
    }
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case "waiter":
        return "bg-blue-100 text-blue-800";
      case "delivery":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="mb-4">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Users className="w-6 h-6" />
          Staff Management
        </h2>
        <Button
          onClick={() => setShowNewStaff(true)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Staff Member
        </Button>
      </div>

      {/* New Staff Form */}
      {showNewStaff && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Staff Member</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={newStaff.first_name}
                  onChange={(e) =>
                    setNewStaff((prev) => ({ ...prev, first_name: e.target.value }))
                  }
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={newStaff.last_name}
                  onChange={(e) =>
                    setNewStaff((prev) => ({ ...prev, last_name: e.target.value }))
                  }
                  placeholder="Enter last name"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={newStaff.email}
                onChange={(e) =>
                  setNewStaff((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter email address"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={newStaff.phone_number}
                onChange={(e) =>
                  setNewStaff((prev) => ({ ...prev, phone_number: e.target.value }))
                }
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <Label htmlFor="role">Role *</Label>
              <Select
                value={newStaff.role}
                onValueChange={(value) =>
                  setNewStaff((prev) => ({ ...prev, role: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="waiter">Waiter</SelectItem>
                  <SelectItem value="delivery">Delivery Person</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleCreateStaff}>
                <Save className="w-4 h-4 mr-2" />
                Create Staff Member
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewStaff(false);
                  setNewStaff({
                    first_name: "",
                    last_name: "",
                    email: "",
                    phone_number: "",
                    role: "",
                  });
                }}
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Staff List */}
      <div className="space-y-4">
        {staff.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">No staff members found</h3>
              <p className="text-gray-600 mb-4">Add your first staff member to get started.</p>
              <Button onClick={() => setShowNewStaff(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Staff Member
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {staff.map((member) => (
              <Card key={member.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">
                        {member.first_name} {member.last_name}
                      </h3>
                      <Badge className={getRoleBadgeColor(member.role)}>
                        {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingStaff(member)}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleDeleteStaff(
                            member.id,
                            `${member.first_name} ${member.last_name}`
                          )
                        }
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      <span>{member.email}</span>
                    </div>
                    {member.phone_number && (
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        <span>{member.phone_number}</span>
                      </div>
                    )}
                    <div className="text-xs text-gray-500 mt-2">
                      Username: {member.username}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default StaffManagement;
