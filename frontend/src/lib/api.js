/**
 * Centralized API Service for DinePulse
 * Handles all backend communication with proper authentication and error handling
 */

class ApiService {
  constructor() {
    this.baseUrl =
      process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
    this.publicUrl = `${this.baseUrl}/api/public`;
  }

  /**
   * Generic API call method with automatic token handling
   */
  async apiCall(url, options = {}) {
    const config = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    // Add authentication token if available
    const token = this.getAuthToken(options.authType);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          response.status,
          errorData.detail || errorData.error || "Request failed",
          errorData
        );
      }

      // Handle empty responses
      if (response.status === 204) {
        return null;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, "Network error", { originalError: error.message });
    }
  }

  /**
   * Get authentication token based on type
   */
  getAuthToken(authType = "customer") {
    switch (authType) {
      case "manager":
        return localStorage.getItem("managerToken");
      case "admin":
        return localStorage.getItem("adminToken");
      case "customer":
      default:
        return localStorage.getItem("customerToken");
    }
  }

  /**
   * Get tenant domain for manager operations
   */
  getTenantDomain(restaurantId) {
    if (!restaurantId) {
      restaurantId = localStorage.getItem("restaurantId");
    }
    return `${restaurantId}.localhost:8000`;
  }

  // ==================== AUTHENTICATION ENDPOINTS ====================

  /**
   * Customer Registration
   * POST /api/public/customer/register/
   */
  async customerRegister(data) {
    const payload = {
      email: data.email,
      username: data.username,
      password: data.password,
      confirm_password: data.confirmPassword,
      phone_number: data.phoneNumber || "",
    };

    return await this.apiCall(`${this.publicUrl}/customer/register/`, {
      method: "POST",
      body: JSON.stringify(payload),
    });
  }

  /**
   * Customer Login
   * POST /api/public/customer/login/
   */
  async customerLogin(credentials) {
    const response = await this.apiCall(`${this.publicUrl}/customer/login/`, {
      method: "POST",
      body: JSON.stringify({
        username: credentials.username,
        password: credentials.password,
      }),
    });

    // Store tokens and user data
    if (response.access) {
      localStorage.setItem("customerToken", response.access);
      localStorage.setItem("customerRefreshToken", response.refresh);
      localStorage.setItem("customerUser", JSON.stringify(response.user));
    }

    return response;
  }

  /**
   * Customer Token Refresh
   * POST /api/public/customer/token/refresh/
   */
  async customerRefreshToken() {
    const refreshToken = localStorage.getItem("customerRefreshToken");
    if (!refreshToken) {
      throw new ApiError(401, "No refresh token available");
    }

    const response = await this.apiCall(
      `${this.publicUrl}/customer/token/refresh/`,
      {
        method: "POST",
        body: JSON.stringify({ refresh: refreshToken }),
      }
    );

    if (response.access) {
      localStorage.setItem("customerToken", response.access);
    }

    return response;
  }

  /**
   * Manager Login (Tenant-specific)
   * POST /api/auth/login/ (on tenant domain)
   */
  async managerLogin(restaurantId, credentials) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/login/`;

    const response = await this.apiCall(url, {
      method: "POST",
      body: JSON.stringify({
        username: credentials.username,
        password: credentials.password,
      }),
    });

    // Store tokens and manager data
    if (response.access) {
      localStorage.setItem("managerToken", response.access);
      localStorage.setItem("managerRefreshToken", response.refresh);
      localStorage.setItem("restaurantId", restaurantId);

      // Get additional restaurant info if needed
      try {
        const restaurantInfo = await this.getRestaurantHome(restaurantId);
        localStorage.setItem("restaurantName", restaurantInfo.restaurant.name);
      } catch (error) {
        console.warn("Could not fetch restaurant info:", error);
      }
    }

    return response;
  }

  /**
   * Manager Token Refresh
   * POST /api/auth/token/refresh/ (on tenant domain)
   */
  async managerRefreshToken(restaurantId) {
    const refreshToken = localStorage.getItem("managerRefreshToken");
    if (!refreshToken) {
      throw new ApiError(401, "No refresh token available");
    }

    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/token/refresh/`;

    const response = await this.apiCall(url, {
      method: "POST",
      body: JSON.stringify({ refresh: refreshToken }),
    });

    if (response.access) {
      localStorage.setItem("managerToken", response.access);
    }

    return response;
  }

  /**
   * Get Cross-Domain Token for Customer
   * GET /api/public/customer/tenant-token/{tenant_id}/
   */
  async getCustomerTenantToken(tenantId) {
    return await this.apiCall(
      `${this.publicUrl}/customer/tenant-token/${tenantId}/`,
      {
        method: "GET",
        authType: "customer",
      }
    );
  }

  /**
   * Exchange Cross-Domain Token
   * POST /api/auth/exchange/ (on tenant domain)
   */
  async exchangeCrossDomainToken(restaurantId, token) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/exchange/`;

    return await this.apiCall(url, {
      method: "POST",
      body: JSON.stringify({ token }),
    });
  }

  // ==================== PUBLIC ENDPOINTS ====================

  /**
   * Get All Restaurants
   * GET /api/public/restaurants/
   */
  async getRestaurants() {
    return await this.apiCall(`${this.publicUrl}/restaurants/`);
  }

  /**
   * Get Billing Plans
   * GET /api/public/billing-plans/
   */
  async getBillingPlans() {
    return await this.apiCall(`${this.publicUrl}/billing-plans/`);
  }

  /**
   * Get Customer Favorite Restaurants
   * GET /api/public/customer/favorite-restaurants/
   */
  async getFavoriteRestaurants() {
    return await this.apiCall(
      `${this.publicUrl}/customer/favorite-restaurants/`,
      {
        method: "GET",
        authType: "customer",
      }
    );
  }

  /**
   * Add/Remove Favorite Restaurant
   * POST /api/public/customer/favorite-restaurants/
   */
  async toggleFavoriteRestaurant(restaurantId) {
    return await this.apiCall(
      `${this.publicUrl}/customer/favorite-restaurants/`,
      {
        method: "POST",
        body: JSON.stringify({ restaurant_id: restaurantId }),
        authType: "customer",
      }
    );
  }

  /**
   * Register Restaurant
   * POST /api/public/register-restaurant/
   */
  async registerRestaurant(data) {
    const formData = new FormData();

    // Add all fields to FormData
    Object.keys(data).forEach((key) => {
      if (data[key] !== null && data[key] !== undefined) {
        if (key === "location" && data[key]) {
          // Handle location as GeoJSON point
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
    });

    return await this.apiCall(`${this.publicUrl}/register-restaurant/`, {
      method: "POST",
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData,
    });
  }

  // ==================== TENANT ENDPOINTS ====================

  /**
   * Get Restaurant Home Data
   * GET / (on tenant domain)
   */
  async getRestaurantHome(restaurantId) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/`;

    return await this.apiCall(url, {
      method: "GET",
    });
  }

  /**
   * Get Restaurant Configuration
   * GET /api/auth/config/ (on tenant domain)
   */
  async getRestaurantConfig(restaurantId) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/config/`;

    return await this.apiCall(url, {
      method: "GET",
      authType: "manager",
    });
  }

  /**
   * Update Restaurant Configuration
   * PUT /api/auth/config/ (on tenant domain)
   */
  async updateRestaurantConfig(restaurantId, data) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/config/`;

    return await this.apiCall(url, {
      method: "PUT",
      body: JSON.stringify(data),
      authType: "manager",
    });
  }

  // ==================== MENU MANAGEMENT ENDPOINTS ====================

  /**
   * Get Menu Categories
   * GET /api/auth/menu/categories/ (on tenant domain)
   */
  async getMenuCategories(restaurantId) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/menu/categories/`;

    return await this.apiCall(url, {
      method: "GET",
      authType: "manager",
    });
  }

  /**
   * Create Menu Category
   * POST /api/auth/menu/categories/ (on tenant domain)
   */
  async createMenuCategory(restaurantId, data) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/menu/categories/`;

    return await this.apiCall(url, {
      method: "POST",
      body: JSON.stringify(data),
      authType: "manager",
    });
  }

  /**
   * Get Menu Items
   * GET /api/auth/menu/menu-items/ (on tenant domain)
   */
  async getMenuItems(restaurantId, params = {}) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const queryString = new URLSearchParams(params).toString();
    const url = `http://${tenantDomain}/api/auth/menu/menu-items/${
      queryString ? "?" + queryString : ""
    }`;

    return await this.apiCall(url, {
      method: "GET",
      authType: "manager",
    });
  }

  /**
   * Create Menu Item
   * POST /api/auth/menu/menu-items/ (on tenant domain)
   */
  async createMenuItem(restaurantId, data) {
    const tenantDomain = this.getTenantDomain(restaurantId);
    const url = `http://${tenantDomain}/api/auth/menu/menu-items/`;

    // Handle FormData for file uploads
    const isFormData = data instanceof FormData;

    return await this.apiCall(url, {
      method: "POST",
      headers: isFormData ? {} : { "Content-Type": "application/json" },
      body: isFormData ? data : JSON.stringify(data),
      authType: "manager",
    });
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Logout user and clear stored data
   */
  logout(userType = "customer") {
    switch (userType) {
      case "manager":
        localStorage.removeItem("managerToken");
        localStorage.removeItem("managerRefreshToken");
        localStorage.removeItem("restaurantId");
        localStorage.removeItem("restaurantName");
        break;
      case "admin":
        localStorage.removeItem("adminToken");
        localStorage.removeItem("adminId");
        localStorage.removeItem("adminEmail");
        break;
      case "customer":
      default:
        localStorage.removeItem("customerToken");
        localStorage.removeItem("customerRefreshToken");
        localStorage.removeItem("customerUser");
        break;
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(userType = "customer") {
    return !!this.getAuthToken(userType);
  }

  /**
   * Get current user data
   */
  getCurrentUser(userType = "customer") {
    switch (userType) {
      case "customer":
        const userData = localStorage.getItem("customerUser");
        return userData ? JSON.parse(userData) : null;
      case "manager":
        return {
          restaurantId: localStorage.getItem("restaurantId"),
          restaurantName: localStorage.getItem("restaurantName"),
        };
      default:
        return null;
    }
  }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
  constructor(status, message, data = {}) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
  }
}

// Export singleton instance
const apiService = new ApiService();
export default apiService;
export { ApiError };
