// "use client";

// import { useState, useEffect } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { Button } from "@/components/ui/button";
// import { Card, CardContent } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { toast } from "sonner";
// import { Check, ChevronsUpDown } from "lucide-react"; // Icons for the dropdown button
// import { cn } from "@/lib/utils"; // Utility for conditional class names (assuming you have this from shadcn/ui setup)
// import {
//   Command,
//   CommandInput,
//   CommandEmpty,
//   CommandGroup,
//   CommandItem,
// } from "@/components/ui/command";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";

// // Import the countries list you provided earlier
// import { countries } from "./countries"; // Ensure 'countries.js' exists in the same directory
// import { useRouter } from "next/navigation";

// export default function RegisterPage() {
//   const [step, setStep] = useState(1);
//   const [countryCode, setCountryCode] = useState("");
//   const [phone, setPhone] = useState("");
//   const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false); // State to control popover open/close

//   const [formData, setFormData] = useState({
//     name: "",
//     username: "",
//     phone: "", // This will be updated with the full combined number
//     email: "",
//     password: "",
//     confirmPassword: "",
//   });

//   const router = useRouter();

//   // Effect to keep formData.phone in sync with countryCode and phone
//   useEffect(() => {
//     setFormData((prev) => ({
//       ...prev,
//       phone: `${countryCode}${phone}`,
//     }));
//   }, [countryCode, phone]);

//   // Handle country selection from the searchable dropdown
//   const handleCountrySelect = (currentCode) => {
//     const country = countries.find(
//       (c) => c.code.toLowerCase() === currentCode.toLowerCase()
//     );
//     if (country) {
//       setCountryCode(country.dialCode);
//       setPhone(""); // Reset phone when country changes, as dial code has changed
//     }
//     setIsCountrySelectOpen(false); // Close the popover after selection
//   };

//   const handlePhoneChange = (e) => {
//     let value = e.target.value;

//     if (value.startsWith("0")) {
//       toast.error("Phone number should not start with 0 for this input field");
//       value = value.replace(/^0+/, "");
//     }

//     // Limit to 9 digits (as per original logic)
//     if (/^\d{0,9}$/.test(value)) {
//       setPhone(value);
//     }
//   };

//   const getFullNumber = () => {
//     return `${countryCode}${phone}`;
//   };

//   const [loading, setLoading] = useState(false);

//   const nextStep = () => setStep((prev) => Math.min(prev + 1, 3));
//   const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));

//   const handleChange = (e) => {
//     setFormData({ ...formData, [e.target.id]: e.target.value });
//   };

//   const validateForm = () => {
//     if (step === 1) {
//       if (!formData.username.trim()) {
//         toast.error("Username is required");
//         return false;
//       }
//       if (!formData.name.trim()) {
//         toast.error("Full Name is required");
//         return false;
//       }
//     }
//     if (step === 2) {
//       // Validate the combined phone number
//       if (!/^\+\d{9,15}$/.test(formData.phone)) {
//         toast.error(
//           "Invalid phone number format. Please ensure it includes the country code and 9-15 digits."
//         );
//         return false;
//       }
//       if (!/\S+@\S+\.\S+/.test(formData.email)) {
//         toast.error("Invalid email address");
//         return false;
//       }
//     }
//     if (step === 3) {
//       if (formData.password.length < 8) {
//         toast.error("Password must be at least 8 characters");
//         return false;
//       }
//       if (formData.password !== formData.confirmPassword) {
//         toast.error("Passwords do not match");
//         return false;
//       }
//     }
//     return true;
//   };

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     if (!validateForm()) return;

//     setLoading(true);
//     try {
//       const res = await fetch(
//         "http://localhost:8000/api/public/customer/register/",
//         {
//           method: "POST",
//           headers: { "Content-Type": "application/json" },
//           body: JSON.stringify({
//             email: formData.email,
//             username: formData.username,
//             password: formData.password,
//             confirm_password: formData.confirmPassword,
//             phone_number: formData.phone, // Already kept in sync by useEffect
//           }),
//         }
//       );

//       const data = await res.json();

//       if (!res.ok) {
//         // Show general error if available
//         if (data.error) toast.error(data.error);

//         // Loop through field-specific errors
//         if (data.details) {
//           Object.entries(data.details).forEach(([field, messages]) => {
//             messages.forEach((msg) => {
//               toast.error(`${field}: ${msg}`);
//             });
//           });
//         }
//         return;
//       }

//       toast.success("Account created successfully! You can now login");
//       setFormData({
//         name: "",
//         username: "",
//         phone: "",
//         email: "",
//         password: "",
//         confirmPassword: "",
//       });
//       setCountryCode("");
//       setPhone("");
//       setStep(1);

//       setTimeout(() => {
//         router.push("/auth/login");
//       }, 7000);
//     } catch (err) {
//       toast.error(err.message);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const stepContent = {
//     1: (
//       <>
//         <div className="grid gap-3">
//           <Label htmlFor="name">Full Name</Label>
//           <Input
//             id="name"
//             type="text"
//             placeholder="John Doe"
//             value={formData.name}
//             onChange={handleChange}
//           />
//         </div>
//         <div className="grid gap-3">
//           <Label htmlFor="username">Username</Label>
//           <Input
//             id="username"
//             type="text"
//             placeholder="johnDoe"
//             value={formData.username}
//             onChange={handleChange}
//           />
//         </div>
//       </>
//     ),
//     2: (
//       <>
//         <div className="flex flex-col gap-3">
//           <Label>Phone Number</Label>
//           {/* Searchable Country Select */}
//           <Popover
//             open={isCountrySelectOpen}
//             onOpenChange={setIsCountrySelectOpen}
//           >
//             <PopoverTrigger asChild>
//               <Button
//                 variant="outline"
//                 role="combobox"
//                 aria-expanded={isCountrySelectOpen}
//                 className="w-full justify-between"
//               >
//                 {countryCode
//                   ? countries.find((c) => c.dialCode === countryCode)?.name ||
//                     "Select a country"
//                   : "Select a country"}
//                 <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
//               </Button>
//             </PopoverTrigger>
//             <PopoverContent className="w-[--radix-popover-trigger-width] p-0 max-h-[300px] overflow-y-auto">
//               <Command>
//                 <CommandInput placeholder="Search country..." />
//                 <CommandEmpty>No country found.</CommandEmpty>
//                 <CommandGroup>
//                   {countries.map((country) => (
//                     <CommandItem
//                       key={country.code}
//                       value={country.code} // Use country code for searchable value
//                       onSelect={(currentCode) => {
//                         handleCountrySelect(currentCode);
//                       }}
//                     >
//                       <Check
//                         className={cn(
//                           "mr-2 h-4 w-4",
//                           countryCode === country.dialCode
//                             ? "opacity-100"
//                             : "opacity-0"
//                         )}
//                       />
//                       {country.name} ({country.dialCode})
//                     </CommandItem>
//                   ))}
//                 </CommandGroup>
//               </Command>
//             </PopoverContent>
//           </Popover>

//           <div className="flex">
//             <span className="px-3 flex items-center bg-gray-200 rounded-l border border-input border-r-0 text-sm text-gray-700">
//               {countryCode || "+---"}
//             </span>
//             <Input
//               type="tel"
//               value={phone}
//               onChange={handlePhoneChange}
//               placeholder="Enter number without 0"
//               className="rounded-l-none"
//             />
//           </div>
//         </div>
//         <div className="grid gap-3">
//           <Label htmlFor="email">Email</Label>
//           <Input
//             id="email"
//             type="email"
//             placeholder="<EMAIL>"
//             value={formData.email}
//             onChange={handleChange}
//           />
//         </div>
//       </>
//     ),
//     3: (
//       <>
//         <div className="grid gap-3">
//           <Label htmlFor="password">Password</Label>
//           <Input
//             id="password"
//             type="password"
//             placeholder="*******"
//             value={formData.password}
//             onChange={handleChange}
//           />
//         </div>
//         <div className="grid gap-3">
//           <Label htmlFor="confirmPassword">Confirm Password</Label>
//           <Input
//             id="confirmPassword"
//             type="password"
//             placeholder="*******"
//             value={formData.confirmPassword}
//             onChange={handleChange}
//           />
//         </div>
//       </>
//     ),
//   };

//   return (
//     <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
//       <Card className="w-full max-w-lg overflow-hidden p-0">
//         <CardContent>
//           <form
//             onSubmit={handleSubmit}
//             className="p-6 md:p-8 flex flex-col gap-6"
//           >
//             <div className="flex flex-col items-center text-center">
//               <h1 className="text-2xl font-bold">
//                 Create Your DinePulse Account
//               </h1>
//               <p className="text-muted-foreground text-balance">
//                 Step {step} of 3
//               </p>
//             </div>

//             <AnimatePresence mode="wait">
//               <motion.div
//                 key={step}
//                 initial={{ opacity: 0, x: 50 }}
//                 animate={{ opacity: 1, x: 0 }}
//                 exit={{ opacity: 0, x: -50 }}
//                 transition={{ duration: 0.3 }}
//                 className="flex flex-col gap-4"
//               >
//                 {stepContent[step]}
//               </motion.div>
//             </AnimatePresence>

//             <div className="flex justify-between">
//               {step > 1 && (
//                 <Button variant="outline" type="button" onClick={prevStep}>
//                   Back
//                 </Button>
//               )}
//               {step < 3 && (
//                 <Button
//                   type="button"
//                   onClick={() => validateForm() && nextStep()}
//                 >
//                   Next
//                 </Button>
//               )}
//               {step === 3 && (
//                 <Button type="submit" disabled={loading}>
//                   {loading ? "Registering..." : "Register"}
//                 </Button>
//               )}
//             </div>

//             <div className="text-center text-sm">
//               Already have an account?{" "}
//               <a href="/auth/login" className="underline underline-offset-4">
//                 Login
//               </a>
//             </div>
//           </form>
//         </CardContent>
//       </Card>
//     </div>
//   );
// }

"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandInput,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Import the countries list you provided earlier
import { countries } from "./countries";
import { useRouter } from "next/navigation";

export default function RegisterPage() {
  const [step, setStep] = useState(1);
  const [countryCode, setCountryCode] = useState("");
  const [phone, setPhone] = useState("");
  const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    username: "",
    phone: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const router = useRouter();

  // Password strength calculation
  const calculatePasswordStrength = (password) => {
    let score = 0;
    const checks = {
      minLength: password.length >= 8,
      hasNumbers: /\d/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasUpperCase: /[A-Z]/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      notTooCommon: !isCommonPassword(password),
      notPersonalInfo: !containsPersonalInfo(password),
    };

    // Calculate score based on criteria
    Object.values(checks).forEach((check) => {
      if (check) score += 1;
    });

    // Additional length bonus
    if (password.length >= 12) score += 1;

    return { score, checks, maxScore: 8 };
  };

  const isCommonPassword = (password) => {
    const commonPasswords = [
      "password",
      "123456",
      "123456789",
      "qwerty",
      "abc123",
      "password123",
      "admin",
      "letmein",
      "welcome",
      "monkey",
    ];
    return commonPasswords.includes(password.toLowerCase());
  };

  const containsPersonalInfo = (password) => {
    const lowerPassword = password.toLowerCase();
    const name = formData.name.toLowerCase();
    const username = formData.username.toLowerCase();

    if (name && name.length > 2 && lowerPassword.includes(name)) return true;
    if (username && username.length > 2 && lowerPassword.includes(username))
      return true;

    return false;
  };

  const getPasswordStrengthColor = (score, maxScore) => {
    const percentage = (score / maxScore) * 100;
    if (percentage < 25) return "bg-red-500";
    if (percentage < 50) return "bg-orange-500";
    if (percentage < 75) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getPasswordStrengthLabel = (score, maxScore) => {
    const percentage = (score / maxScore) * 100;
    if (percentage < 25) return "Very Weak";
    if (percentage < 50) return "Weak";
    if (percentage < 75) return "Good";
    if (percentage < 90) return "Strong";
    return "Very Strong";
  };

  const passwordStrength = calculatePasswordStrength(formData.password);

  // Effect to keep formData.phone in sync with countryCode and phone
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      phone: `${countryCode}${phone}`,
    }));
  }, [countryCode, phone]);

  // Handle country selection from the searchable dropdown
  const handleCountrySelect = (currentCode) => {
    const country = countries.find(
      (c) => c.code.toLowerCase() === currentCode.toLowerCase()
    );
    if (country) {
      setCountryCode(country.dialCode);
      setPhone("");
    }
    setIsCountrySelectOpen(false);
  };

  const handlePhoneChange = (e) => {
    let value = e.target.value;

    if (value.startsWith("0")) {
      toast.error("Phone number should not start with 0 for this input field");
      value = value.replace(/^0+/, "");
    }

    if (/^\d{0,9}$/.test(value)) {
      setPhone(value);
    }
  };

  const getFullNumber = () => {
    return `${countryCode}${phone}`;
  };

  const [loading, setLoading] = useState(false);

  const nextStep = () => setStep((prev) => Math.min(prev + 1, 3));
  const prevStep = () => setStep((prev) => Math.max(prev - 1, 1));

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const validateForm = () => {
    if (step === 1) {
      if (!formData.username.trim()) {
        toast.error("Username is required");
        return false;
      }
      if (!formData.name.trim()) {
        toast.error("Full Name is required");
        return false;
      }
    }
    if (step === 2) {
      if (!/^\+\d{9,15}$/.test(formData.phone)) {
        toast.error(
          "Invalid phone number format. Please ensure it includes the country code and 9-15 digits."
        );
        return false;
      }
      if (!/\S+@\S+\.\S+/.test(formData.email)) {
        toast.error("Invalid email address");
        return false;
      }
    }
    if (step === 3) {
      if (formData.password.length < 8) {
        toast.error("Password must be at least 8 characters");
        return false;
      }
      if (formData.password !== formData.confirmPassword) {
        toast.error("Passwords do not match");
        return false;
      }

      // Check if password meets minimum requirements
      const { score, maxScore } = passwordStrength;
      if (score < 4) {
        toast.error("Password is too weak. Please meet more requirements.");
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      const res = await fetch(
        "http://localhost:8000/api/public/customer/register/",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            email: formData.email,
            username: formData.username,
            password: formData.password,
            confirm_password: formData.confirmPassword,
            phone_number: formData.phone,
          }),
        }
      );

      const data = await res.json();

      if (!res.ok) {
        if (data.error) toast.error(data.error);

        if (data.details) {
          Object.entries(data.details).forEach(([field, messages]) => {
            messages.forEach((msg) => {
              toast.error(`${field}: ${msg}`);
            });
          });
        }
        return;
      }

      toast.success("Account created successfully! You can now login");
      setFormData({
        name: "",
        username: "",
        phone: "",
        email: "",
        password: "",
        confirmPassword: "",
      });
      setCountryCode("");
      setPhone("");
      setStep(1);

      setTimeout(() => {
        router.push("/auth/login");
      }, 7000);
    } catch (err) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const stepContent = {
    1: (
      <>
        <div className="grid gap-3">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            type="text"
            placeholder="John Doe"
            value={formData.name}
            onChange={handleChange}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="username">Username</Label>
          <Input
            id="username"
            type="text"
            placeholder="johnDoe"
            value={formData.username}
            onChange={handleChange}
          />
        </div>
      </>
    ),
    2: (
      <>
        <div className="flex flex-col gap-3">
          <Label>Phone Number</Label>
          <Popover
            open={isCountrySelectOpen}
            onOpenChange={setIsCountrySelectOpen}
          >
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={isCountrySelectOpen}
                className="w-full justify-between"
              >
                {countryCode
                  ? countries.find((c) => c.dialCode === countryCode)?.name ||
                    "Select a country"
                  : "Select a country"}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[--radix-popover-trigger-width] p-0 max-h-[300px] overflow-y-auto">
              <Command>
                <CommandInput placeholder="Search country..." />
                <CommandEmpty>No country found.</CommandEmpty>
                <CommandGroup>
                  {countries.map((country) => (
                    <CommandItem
                      key={country.code}
                      value={country.code}
                      onSelect={(currentCode) => {
                        handleCountrySelect(currentCode);
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          countryCode === country.dialCode
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {country.name} ({country.dialCode})
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>

          <div className="flex">
            <span className="px-3 flex items-center bg-gray-200 rounded-l border border-input border-r-0 text-sm text-gray-700">
              {countryCode || "+---"}
            </span>
            <Input
              type="tel"
              value={phone}
              onChange={handlePhoneChange}
              placeholder="Enter number without 0"
              className="rounded-l-none"
            />
          </div>
        </div>
        <div className="grid gap-3">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={handleChange}
          />
        </div>
      </>
    ),
    3: (
      <>
        <div className="grid gap-3">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="*******"
            value={formData.password}
            onChange={handleChange}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            placeholder="*******"
            value={formData.confirmPassword}
            onChange={handleChange}
          />
        </div>

        {/* Password Requirements Section */}
        {formData.password && (
          <div className="mt-4 p-4 bg-muted rounded-lg border">
            <div className="mb-3">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Password Strength</span>
                <span
                  className={cn(
                    "text-xs font-medium",
                    passwordStrength.score < 4
                      ? "text-red-600"
                      : passwordStrength.score < 6
                      ? "text-orange-600"
                      : passwordStrength.score < 7
                      ? "text-yellow-600"
                      : "text-green-600"
                  )}
                >
                  {getPasswordStrengthLabel(
                    passwordStrength.score,
                    passwordStrength.maxScore
                  )}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={cn(
                    "h-2 rounded-full transition-all duration-300",
                    getPasswordStrengthColor(
                      passwordStrength.score,
                      passwordStrength.maxScore
                    )
                  )}
                  style={{
                    width: `${
                      (passwordStrength.score / passwordStrength.maxScore) * 100
                    }%`,
                  }}
                />
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">
                Password Requirements:
              </h4>
              <ul className="space-y-1 text-xs">
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.minLength ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.minLength
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    At least 8 characters long
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.hasNumbers ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.hasNumbers
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Contains at least one number
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.hasLowerCase ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.hasLowerCase
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Contains lowercase letters
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.hasUpperCase ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.hasUpperCase
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Contains uppercase letters
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.hasSpecialChar ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.hasSpecialChar
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Contains special characters (!@#$%^&*)
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.notTooCommon ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.notTooCommon
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Not a commonly used password
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  {passwordStrength.checks.notPersonalInfo ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <X className="h-3 w-3 text-red-600" />
                  )}
                  <span
                    className={
                      passwordStrength.checks.notPersonalInfo
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    Doesn't contain your name or username
                  </span>
                </li>
              </ul>
            </div>
          </div>
        )}
      </>
    ),
  };

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <Card className="w-full max-w-lg overflow-hidden p-0">
        <CardContent>
          <form
            onSubmit={handleSubmit}
            className="p-6 md:p-8 flex flex-col gap-6"
          >
            <div className="flex flex-col items-center text-center">
              <h1 className="text-2xl font-bold">
                Create Your DinePulse Account
              </h1>
              <p className="text-muted-foreground text-balance">
                Step {step} of 3
              </p>
            </div>

            <AnimatePresence mode="wait">
              <motion.div
                key={step}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col gap-4"
              >
                {stepContent[step]}
              </motion.div>
            </AnimatePresence>

            <div className="flex justify-between">
              {step > 1 && (
                <Button variant="outline" type="button" onClick={prevStep}>
                  Back
                </Button>
              )}
              {step < 3 && (
                <Button
                  type="button"
                  onClick={() => validateForm() && nextStep()}
                >
                  Next
                </Button>
              )}
              {step === 3 && (
                <Button type="submit" disabled={loading}>
                  {loading ? "Registering..." : "Register"}
                </Button>
              )}
            </div>

            <div className="text-center text-sm">
              Already have an account?{" "}
              <a href="/auth/login" className="underline underline-offset-4">
                Login
              </a>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
