"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export default function LoginPage() {
  const [uname, setUName] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  const router = useRouter();

  const login = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const res = await fetch(
        "http://localhost:8000/api/public/customer/login/",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            username: uname, // assuming username is the email
            password: password,
          }),
        }
      );

      const data = await res.json();

      if (res.ok) {
        //store tokens in localStorage
        localStorage.setItem("accessToken", data.access);
        localStorage.setItem("refreshToken", data.refresh);

        // Store user data
        localStorage.setItem("user", JSON.stringify(data.user));

        toast.success("Login successful! Redirecting...");

        // Role-based redirection
        const userRole = data.user?.role;

        // Redirect to customer dashboard no matter who you are
        router.push("/customer");
      } else {
        // Show server-provided error if available
        if (data?.error) {
          toast.error(data.error);
        } else if (data?.detail) {
          toast.error(data.detail);
        } else {
          toast.error("Login failed. Please check your credentials.");
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <div className={"flex flex-col gap-6"}>
          <Card className="overflow-hidden p-0 border-2 border-orange-500">
            <CardContent className="grid p-0 md:grid-cols-2">
              <form onSubmit={login} className="p-6 md:p-8">
                <div className="flex flex-col gap-6">
                  <div className="flex flex-col items-center text-center">
                    <h1 className="text-2xl font-bold">
                      Glad to have you back!
                    </h1>
                    <p className="text-muted-foreground text-balance">
                      Login to your Easy Eats account
                    </p>
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="uname">Username</Label>
                    <Input
                      id="uname"
                      type="text"
                      placeholder="--"
                      value={uname}
                      onChange={(e) => setUName(e.target.value)}
                      required
                    />
                  </div>
                  <div className="grid gap-3">
                    <div className="flex items-center">
                      <Label htmlFor="password">Password</Label>
                      <a
                        href="#"
                        className="ml-auto text-sm underline-offset-2 hover:underline"
                      >
                        Forgot your password?
                      </a>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      placeholder="******"
                    />
                  </div>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "Logging in..." : "Login"}
                  </Button>
                  <div className="ml-auto text-center text-sm">
                    Don&apos;t have an account?{" "}
                    <a
                      href="/auth/register"
                      className="underline underline-offset-4"
                    >
                      Register
                    </a>
                  </div>
                </div>
              </form>
              <div className="grid place-items-center dark:bg-gray-700 border-l border-orange-400 bg-white relative hidden md:block">
                <Image
                  src="/lock.png"
                  alt="Image"
                  height={400}
                  width={400}
                  className="h-full inset-0 object-contain dark:brightness-[0.2] dark:grayscale"
                />
              </div>
            </CardContent>
          </Card>
          <div className="text-muted-foreground text-center text-xs text-balance">
            By clicking continue, you agree to our{" "}
            <a href="#" className="underline underline-offset-4">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="underline underline-offset-4">
              Privacy Policy
            </a>
            .
          </div>
        </div>
      </div>
    </div>
  );
}
