"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, MapPin, Phone, Mail, ArrowLeft, Navigation, ChefHat, Heart, Share2, Clock } from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"
import RouteMap from "@/components/route-map"
import Image from "next/image"

export default function RestaurantDetailsPage() {
  const params = useParams()
  const [activeTab, setActiveTab] = useState("menu")
  const [userLocation, setUserLocation] = useState(null)
  const [showRouteDialog, setShowRouteDialog] = useState(false)
  const [userRating, setUserRating] = useState(0)
  const [userReview, setUserReview] = useState("")
  const [isSubmittingReview, setIsSubmittingReview] = useState(false)
  const [restaurant, setRestaurant] = useState(null)
  const [menuItems, setMenuItems] = useState([])
  const [loading, setLoading] = useState(true)

  // Fetch restaurant details and menu
  useEffect(() => {
    const fetchRestaurantData = async () => {
      try {
        setLoading(true)

        // Try to fetch restaurant details from API
        const restaurantResponse = await fetch(`http://localhost:8000/api/public/restaurants/${params.id}/`)

        if (restaurantResponse.ok) {
          const restaurantData = await restaurantResponse.json()
          setRestaurant({
            id: restaurantData.id,
            name: restaurantData.restaurant_name || restaurantData.name,
            cuisine: restaurantData.cuisine || "Various",
            rating: restaurantData.rating || 4.5,
            reviews: restaurantData.review_count || 324,
            coordinates: restaurantData.coordinates || [-15.7861, 35.0058],
            address: restaurantData.address || "Restaurant Address",
            phone: restaurantData.restaurant_phone || restaurantData.phone || "+265991234567",
            email: restaurantData.email || "<EMAIL>",
            hours: restaurantData.hours || "Mon-Sun: 11:00 AM - 10:00 PM",
            priceRange: restaurantData.price_range || "MWK 10,000 - MWK 30,000",
            isOpen: restaurantData.is_open !== undefined ? restaurantData.is_open : true,
            description: restaurantData.description || "Delicious food with great service",
            images: [
              restaurantData.banner,
              restaurantData.logo,
              "/placeholder.svg?height=400&width=600"
            ].filter(Boolean),
          })

          // Try to fetch menu items for this restaurant
          try {
            const menuResponse = await fetch(`http://localhost:8000/api/public/restaurants/${params.id}/menu/`)
            if (menuResponse.ok) {
              const menuData = await menuResponse.json()
              setMenuItems(menuData.results || menuData)
            }
          } catch (error) {
            console.log("Menu data not available")
          }
        } else {
          // Fallback to mock data
          setRestaurant({
            id: params.id,
            name: "Blantyre Delights",
            cuisine: "Malawian",
            rating: 4.8,
            reviews: 324,
            coordinates: [-15.7861, 35.0058],
            address: "Main St, Blantyre City Centre",
            phone: "+265991234567",
            email: "<EMAIL>",
            hours: "Mon-Sun: 11:00 AM - 10:00 PM",
            priceRange: "MWK 10,000 - MWK 30,000",
            isOpen: true,
            description: "Traditional Malawian dishes with a modern twist. Fresh ingredients, creative presentations, and a cozy atmosphere make this the perfect spot for any occasion.",
            images: [
              "/placeholder.svg?height=400&width=600",
              "/placeholder.svg?height=400&width=600",
              "/placeholder.svg?height=400&width=600",
            ],
          })
        }
      } catch (error) {
        console.error("Failed to fetch restaurant data:", error)
        // Fallback to mock data
        setRestaurant({
          id: params.id,
          name: "Blantyre Delights",
          cuisine: "Malawian",
          rating: 4.8,
          reviews: 324,
          coordinates: [-15.7861, 35.0058],
          address: "Main St, Blantyre City Centre",
          phone: "+265991234567",
          email: "<EMAIL>",
          hours: "Mon-Sun: 11:00 AM - 10:00 PM",
          priceRange: "MWK 10,000 - MWK 30,000",
          isOpen: true,
          description: "Traditional Malawian dishes with a modern twist.",
          images: [
            "/placeholder.svg?height=400&width=600",
          ],
        })
      } finally {
        setLoading(false)
      }
    }

    fetchRestaurantData()
  }, [params.id])

  // Mock reviews data (could be fetched from API in the future)
  const reviews = [
    {
      id: 1,
      user: {
        name: "Chikondi Banda",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      rating: 5,
      comment: "Absolutely loved the chambo fish! The service was excellent too.",
      date: "2025-06-10",
    },
    {
      id: 2,
      user: {
        name: "Mphatso Nkhoma",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      rating: 4,
      comment: "Great atmosphere and delicious nsima with beef stew. Highly recommended!",
      date: "2025-06-08",
    },
    {
      id: 3,
      user: {
        name: "Thoko Phiri",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      rating: 3,
      comment: "The grilled goat was good, but service was a bit slow during peak hours.",
      date: "2025-06-05",
    },
  ],
  addressCoordinates: [-15.7861, 35.0058],
  distance: "0.5 km",
  deliveryTime: "25-35 min",
  status: "Active",
  staff: 15,
  image: "/placeholder.svg?height=300&width=500",
};


  const [dishes, setDishes] = useState([
    {
      id: 1,
      name: "Grilled Salmon",
      category: "Main Course",
      price: 24000.99,
      description: "Fresh Atlantic salmon with lemon herb butter, served with roasted vegetables",
      status: "Available",
      popular: true,
    },
    {
      id: 2,
      name: "Caesar Salad",
      category: "Appetizer",
      price: 12000.99,
      description: "Crisp romaine lettuce, parmesan cheese, croutons, and our signature dressing",
      status: "Available",
      popular: false,
    },
    {
      id: 3,
      name: "Chocolate Cake",
      category: "Dessert",
      price: 8000.99,
      description: "Rich chocolate cake with layers of chocolate ganache and fresh berries",
      status: "Out of Stock",
      popular: false,
    },
    {
      id: 4,
      name: "Beef Burger",
      category: "Main Course",
      price: 16000.99,
      description: "Juicy beef patty with lettuce, tomato, onion, and special sauce on a brioche bun",
      status: "Available",
      popular: true,
    },
  ])

  const [users, setUsers] = useState([
    {
      id: 1,
      name: "John Smith",
      role: "Branch Manager",
      email: "<EMAIL>",
      phone: "+1234567890",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      name: "Sarah Johnson",
      role: "Waiter",
      email: "<EMAIL>",
      phone: "+1234567891",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      name: "Mike Wilson",
      role: "Delivery Personnel",
      email: "<EMAIL>",
      phone: "+1234567892",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 4,
      name: "Emily Davis",
      role: "Waiter",
      email: "<EMAIL>",
      phone: "+1234567893",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ])

  const [newDish, setNewDish] = useState({
    name: "",
    category: "",
    price: "",
    description: "",
    status: "Available",
  })

  const [newUser, setNewUser] = useState({
    name: "",
    role: "",
    email: "",
    phone: "",
  })

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation([position.coords.latitude, position.coords.longitude])
        },
        (error) => {
          console.error("Error getting location:", error)
        },
      )
    }
  }, [])

  const handleAddDish = () => {
    if (newDish.name && newDish.category && newDish.price) {
      setDishes([
        ...dishes,
        {
          id: dishes.length + 1,
          ...newDish,
        },
      ])
      setNewDish({ name: "", category: "", price: "", description: "", status: "Available" })
      // setIsAddDishOpen(false) // Removed as not used in updates
    }
  }

  const handleAddUser = () => {
    if (newUser.name && newUser.role && newUser.email) {
      setUsers([
        ...users,
        {
          id: users.length + 1,
          ...newUser,
          avatar: "/placeholder.svg?height=40&width=40",
        },
      ])
      setNewUser({ name: "", role: "", email: "", phone: "" })
      // setIsAddUserOpen(false) // Removed as not used in updates
    }
  }

  const handleGetDirections = () => {
    if (userLocation) {
      setShowRouteDialog(true)
    } else {
      // Fallback to external maps
      const query = encodeURIComponent(restaurant.address)
      window.open(`https://www.openstreetmap.org/search?query=${query}`, "_blank")
    }
  }

  const handleSubmitReview = async () => {
    if (userRating === 0) return

    setIsSubmittingReview(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Here you would typically save the review to your database
    console.log("Review submitted:", { rating: userRating, review: userReview })

    // Add to visit history
    const visitHistory = JSON.parse(localStorage.getItem("visitHistory") || "[]")
    const newVisit = {
      id: Date.now(),
      restaurantId: restaurant.id,
      restaurantName: restaurant.name,
      date: new Date().toISOString(),
      rating: userRating,
      review: userReview,
    }
    visitHistory.unshift(newVisit)
    localStorage.setItem("visitHistory", JSON.stringify(visitHistory))

    setUserRating(0)
    setUserReview("")
    setIsSubmittingReview(false)
    alert("Thank you for your review!")
  }

  const renderStars = (rating, interactive = false, onRate = null) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 ${
              star <= rating ? "text-yellow-500 fill-current" : "text-gray-300"
            } ${interactive ? "cursor-pointer hover:text-yellow-400" : ""}`}
            onClick={interactive ? () => onRate(star) : undefined}
          />
        ))}
      </div>
    )
  }

  const groupedMenuItems = dishes.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = []
    }
    acc[item.category].push(item)
    return acc
  }, {})

  const getRoleColor = (role) => {
    switch (role) {
      case "Branch Manager":
        return "bg-purple-100 text-purple-800"
      case "Waiter":
        return "bg-blue-100 text-blue-800"
      case "Delivery Personnel":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "Available":
        return "bg-green-100 text-green-800"
      case "Out of Stock":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading restaurant details...</p>
        </div>
      </div>
    )
  }

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Restaurant not found</p>
          <Link href="/customer/restaurants" className="text-orange-600 hover:underline mt-2 inline-block">
            Back to restaurants
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <Link href="/customer/restaurants" className="flex items-center gap-2">
              <ArrowLeft className="h-5 w-5" />
              <Image alt="EasyEats" src={'/logo1.png'} width={50} height={50}/>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Restaurant Header */}
        <Card className="mb-8">
          <CardContent className="p-0">
            <div className="relative">
              <img
                src={restaurant.images[0] || "/placeholder.svg"}
                alt={restaurant.name}
                className="w-full h-64 md:h-80 object-cover"
              />
              <div className="absolute top-4 right-4 flex gap-2">
                <Button variant="secondary" size="sm">
                  <Heart className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="secondary" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>

            <div className="p-6">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold text-gray-900">{restaurant.name}</h1>
                    <Badge className={restaurant.isOpen ? "bg-green-600" : "bg-red-600"}>
                      {restaurant.isOpen ? "Open" : "Closed"}
                    </Badge>
                    <Badge variant="secondary">{restaurant.priceRange}</Badge>
                  </div>

                  <div className="flex items-center gap-2 mb-3">
                    {renderStars(restaurant.rating)}
                    <span className="font-medium">{restaurant.rating}</span>
                    <span className="text-gray-500">({restaurant.reviews.length} reviews)</span>
                    <span className="text-gray-400">•</span>
                    <span className="text-orange-600 font-medium">{restaurant.cuisine}</span>
                  </div>

                  <p className="text-gray-600 mb-4">{restaurant.description}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{restaurant.address}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{restaurant.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span>{restaurant.hours}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{restaurant.email}</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-3 md:min-w-[200px]">
                  <Button onClick={handleGetDirections} className="flex items-center gap-2">
                    <Navigation className="h-4 w-4" />
                    Get Directions
                  </Button>
                  <div className="text-center text-sm text-gray-600">
                    <div>{restaurant.distance} away</div>
                    <div>{restaurant.deliveryTime}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="menu">Menu</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="info">Info</TabsTrigger>
          </TabsList>

          {/* Menu Tab */}
          <TabsContent value="menu" className="space-y-6">
            {Object.entries(groupedMenuItems).map(([category, items]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle>{category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {items.map((item) => (
                      <div key={item.id} className="flex gap-4">
                        <img
                          src={item.image || "/placeholder.svg"}
                          alt={item.name}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <div className="flex justify-between items-start mb-1">
                            <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                              {item.name}
                              {item.popular && (
                                <Badge variant="secondary" className="text-xs">
                                  Popular
                                </Badge>
                              )}
                            </h4>
                            <span className="font-bold text-orange-600">MWK{item.price}</span>
                          </div>
                          <p className="text-sm text-gray-600">{item.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          {/* Reviews Tab */}
          <TabsContent value="reviews" className="space-y-6">
            {/* Add Review */}
            <Card>
              <CardHeader>
                <CardTitle>Write a Review</CardTitle>
                <div className="text-muted">Share your experience with others</div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Your Rating</label>
                  {renderStars(userRating, true, setUserRating)}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Your Review</label>
                  <Textarea
                    placeholder="Tell others about your experience..."
                    value={userReview}
                    onChange={(e) => setUserReview(e.target.value)}
                    rows={4}
                  />
                </div>
                <Button
                  onClick={handleSubmitReview}
                  disabled={userRating === 0 || isSubmittingReview}
                  className="w-full md:w-auto"
                >
                  {isSubmittingReview ? "Submitting..." : "Submit Review"}
                </Button>
              </CardContent>
            </Card>

            {/* Reviews List */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Reviews</CardTitle>
                <div>{restaurant.reviews.length} reviews</div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {restaurant.reviews.map((review) => (
                    <div key={review.id} className="flex gap-4">
                      <Avatar>
                        <AvatarImage src={review.user.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {review.user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-semibold text-gray-900">{review.user.name}</h4>
                            <div className="flex items-center gap-2">
                              {renderStars(review.rating)}
                              <span className="text-sm text-gray-500">{review.date}</span>
                            </div>
                          </div>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Info Tab */}
          <TabsContent value="info" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Address</p>
                      <p className="text-gray-600">{restaurant.address}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-gray-600">{restaurant.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-gray-600">{restaurant.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Hours</p>
                      <p className="text-gray-600">{restaurant.hours}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Additional Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="font-medium mb-1">Cuisine Type</p>
                    <Badge>{restaurant.cuisine}</Badge>
                  </div>
                  <div>
                    <p className="font-medium mb-1">Price Range</p>
                    <Badge variant="secondary">{restaurant.priceRange}</Badge>
                  </div>
                  <div>
                    <p className="font-medium mb-1">Distance</p>
                    <p className="text-gray-600">{restaurant.distance} from your location</p>
                  </div>
                  <div>
                    <p className="font-medium mb-1">Estimated Delivery</p>
                    <p className="text-gray-600">{restaurant.deliveryTime}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Route Dialog */}
        <Dialog open={showRouteDialog} onOpenChange={setShowRouteDialog}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Directions to {restaurant.name}</DialogTitle>
              <DialogDescription>Route from your location to the restaurant</DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <RouteMap
                userLocation={userLocation}
                restaurantLocation={restaurant.coordinates}
                restaurantName={restaurant.name}
                height="500px"
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
