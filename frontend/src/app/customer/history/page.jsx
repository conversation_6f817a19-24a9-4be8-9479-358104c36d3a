"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Calendar, ChefHat, Trash2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import CustomerHeader from "@/components/customer-header"

export default function HistoryPage() {
  const [visitHistory, setVisitHistory] = useState([])

  useEffect(() => {
    // Load visit history from localStorage
    const history = JSON.parse(localStorage.getItem("visitHistory") || "[]")
    setVisitHistory(history)
  }, [])

  const handleDeleteVisit = (visitId) => {
    const updatedHistory = visitHistory.filter((visit) => visit.id !== visitId)
    setVisitHistory(updatedHistory)
    localStorage.setItem("visitHistory", JSON.stringify(updatedHistory))
  }

  const renderStars = (rating) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star key={star} className={`h-4 w-4 ${star <= rating ? "text-yellow-500 fill-current" : "text-gray-300"}`} />
        ))}
      </div>
    )
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Mock additional restaurant data for display
  const getRestaurantData = (restaurantId) => {
    const restaurants = {
      1: {
        cuisine: "American",
        image: "/placeholder.svg?height=100&width=100",
        address: "123 Main St, Downtown",
      },
      2: {
        cuisine: "Italian",
        image: "/placeholder.svg?height=100&width=100",
        address: "456 Pizza Ave, Little Italy",
      },
      3: {
        cuisine: "Japanese",
        image: "/placeholder.svg?height=100&width=100",
        address: "789 Sushi Blvd, Midtown",
      },
    }
    return (
      restaurants[restaurantId] || {
        cuisine: "Various",
        image: "/placeholder.svg?height=100&width=100",
        address: "Unknown location",
      }
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">

      <CustomerHeader/>

      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Visit History</h1>
          <p className="text-gray-600">
            Your restaurant visits and reviews
            {visitHistory.length > 0 && ` (${visitHistory.length} visits)`}
          </p>
        </div>

        {visitHistory.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Calendar className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No visit history yet</h3>
              <p className="text-gray-600 mb-6">
                Start exploring restaurants and rating them to build your visit history!
              </p>
              <Link href="/customer/restaurants">
                <Button>Browse Restaurants</Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {visitHistory.map((visit) => {
              const restaurantData = getRestaurantData(visit.restaurantId)
              return (
                <Card key={visit.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      <img
                        src={restaurantData.image || "/placeholder.svg"}
                        alt={visit.restaurantName}
                        className="w-20 h-20 object-cover rounded-lg"
                      />

                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-1">{visit.restaurantName}</h3>
                            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                              <Badge variant="secondary">{restaurantData.cuisine}</Badge>
                              <span>•</span>
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {restaurantData.address}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteVisit(visit.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="flex items-center gap-3 mb-3">
                          <div className="flex items-center gap-2">
                            {renderStars(visit.rating)}
                            <span className="font-medium">{visit.rating}/5</span>
                          </div>
                          <span className="text-gray-400">•</span>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            {formatDate(visit.date)}
                          </div>
                        </div>

                        {visit.review && (
                          <div className="bg-gray-50 rounded-lg p-4 mb-4">
                            <p className="text-gray-700 italic">"{visit.review}"</p>
                          </div>
                        )}

                        <div className="flex gap-3">
                          <Link href={`/restaurants/${visit.restaurantId}`}>
                            <Button variant="outline" size="sm">
                              View Restaurant
                            </Button>
                          </Link>
                          <Button variant="outline" size="sm">
                            Order Again
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}

        {visitHistory.length > 0 && (
          <div className="mt-8 text-center">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Your Dining Stats</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{visitHistory.length}</div>
                    <p className="text-sm text-gray-600">Restaurants Visited</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {visitHistory.filter((v) => v.rating >= 4).length}
                    </div>
                    <p className="text-sm text-gray-600">Highly Rated (4+ stars)</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {(visitHistory.reduce((sum, v) => sum + v.rating, 0) / visitHistory.length).toFixed(1)}
                    </div>
                    <p className="text-sm text-gray-600">Average Rating Given</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
