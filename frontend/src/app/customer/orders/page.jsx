"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ShoppingCart,
  Search,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  DollarSign,
  MapPin,
  Phone,
  Calendar,
  Package,
  Star,
  RefreshCw,
} from "lucide-react";
import CustomerHeader from "@/components/customer-header";

export default function CustomerOrders() {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Get auth headers for API calls
  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("customerToken") ||
      localStorage.getItem("accessToken");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  };

  // Fetch orders from API
  const fetchOrders = async () => {
    try {
      setLoading(true);

      // Try multiple potential endpoints for customer orders
      const potentialEndpoints = [
        "http://localhost:8000/api/auth/orders/orders/",
        "http://localhost:8000/api/public/customer/orders/",
        "http://localhost:8000/api/tenant/orders/",
      ];

      let ordersData = [];
      let success = false;

      for (const endpoint of potentialEndpoints) {
        try {
          const response = await fetch(endpoint, {
            headers: getAuthHeaders(),
          });

          if (response.ok) {
            const data = await response.json();
            ordersData = data.results || data;
            success = true;
            break;
          }
        } catch (error) {
          console.log(`Endpoint ${endpoint} not available`);
        }
      }

      if (!success) {
        // Fallback to mock data if API fails
        ordersData = [
          {
            id: 1,
            restaurant: {
              id: 1,
              name: "Mzuzu Grill",
              logo: "/placeholder.svg?height=60&width=60",
            },
            status: "pending",
            created_at: "2024-01-15T10:30:00Z",
            updated_at: "2024-01-15T10:30:00Z",
            total_amount: "25.50",
            payment_method: "cash",
            is_paid: false,
            delivery_address: "123 Main St, Lilongwe",
            delivery_instructions: "Ring the bell twice",
            estimated_delivery: "2024-01-15T11:30:00Z",
            items: [
              {
                id: 1,
                name: "Grilled Chicken",
                quantity: 2,
                price: "12.75",
                special_instructions: "Extra spicy",
              },
            ],
            payment: null,
          },
          {
            id: 2,
            restaurant: {
              id: 2,
              name: "Lake Malawi Fish Spot",
              logo: "/placeholder.svg?height=60&width=60",
            },
            status: "delivered",
            created_at: "2024-01-14T09:15:00Z",
            updated_at: "2024-01-14T10:45:00Z",
            total_amount: "18.75",
            payment_method: "card",
            is_paid: true,
            delivery_address: "456 Oak Ave, Blantyre",
            delivery_instructions: "",
            estimated_delivery: "2024-01-14T10:15:00Z",
            items: [
              {
                id: 2,
                name: "Grilled Fish with Nsima",
                quantity: 1,
                price: "18.75",
                special_instructions: "",
              },
            ],
            payment: {
              id: 1,
              amount: "18.75",
              transaction_id: "TXN123456",
              payment_method: "card",
              status: "completed",
              created_at: "2024-01-14T09:20:00Z",
            },
          },
          {
            id: 3,
            restaurant: {
              id: 3,
              name: "Nsima & More",
              logo: "/placeholder.svg?height=60&width=60",
            },
            status: "on_delivery",
            created_at: "2024-01-15T12:00:00Z",
            updated_at: "2024-01-15T12:45:00Z",
            total_amount: "32.00",
            payment_method: "online",
            is_paid: true,
            delivery_address: "789 Pine St, Mzuzu",
            delivery_instructions: "Leave at the gate",
            estimated_delivery: "2024-01-15T13:00:00Z",
            items: [
              {
                id: 3,
                name: "Traditional Nsima Platter",
                quantity: 2,
                price: "16.00",
                special_instructions: "Extra vegetables",
              },
            ],
            payment: {
              id: 2,
              amount: "32.00",
              transaction_id: "TXN789012",
              payment_method: "online",
              status: "completed",
              created_at: "2024-01-15T12:05:00Z",
            },
          },
        ];
      }

      setOrders(ordersData);
      setFilteredOrders(ordersData);
    } catch (error) {
      console.error("Error fetching orders:", error);
      setOrders([]);
      setFilteredOrders([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // Refresh orders
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchOrders();
    setRefreshing(false);
  };

  // Filter orders based on search and status
  useEffect(() => {
    let filtered = orders;

    if (searchQuery) {
      filtered = filtered.filter(
        (order) =>
          order.restaurant?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.id.toString().includes(searchQuery) ||
          order.items?.some((item) =>
            item.name?.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    setFilteredOrders(filtered);
  }, [searchQuery, statusFilter, orders]);

  // Get status badge color
  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "pending":
        return "secondary";
      case "confirmed":
        return "default";
      case "preparing":
        return "outline";
      case "on_delivery":
        return "default";
      case "delivered":
        return "default";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "confirmed":
        return <CheckCircle className="h-4 w-4" />;
      case "preparing":
        return <Package className="h-4 w-4" />;
      case "on_delivery":
        return <Truck className="h-4 w-4" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4" />;
      case "cancelled":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Calculate order statistics
  const orderStats = {
    total: orders.length,
    active: orders.filter((o) =>
      ["pending", "confirmed", "preparing", "on_delivery"].includes(o.status)
    ).length,
    delivered: orders.filter((o) => o.status === "delivered").length,
    cancelled: orders.filter((o) => o.status === "cancelled").length,
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <CustomerHeader />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <CustomerHeader />

      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
            <p className="text-gray-600">Track and manage your food orders</p>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Orders
                  </p>
                  <p className="text-2xl font-bold">{orderStats.total}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Active Orders
                  </p>
                  <p className="text-2xl font-bold text-orange-600">
                    {orderStats.active}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Delivered</p>
                  <p className="text-2xl font-bold text-green-600">
                    {orderStats.delivered}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cancelled</p>
                  <p className="text-2xl font-bold text-red-600">
                    {orderStats.cancelled}
                  </p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by restaurant, order ID, or food item..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Orders</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="preparing">Preparing</SelectItem>
                    <SelectItem value="on_delivery">On Delivery</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchQuery || statusFilter !== "all"
                  ? "No orders found"
                  : "No orders yet"}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "Start ordering from your favorite restaurants!"}
              </p>
              {!searchQuery && statusFilter === "all" && (
                <Button asChild>
                  <a href="/customer/restaurants">Browse Restaurants</a>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <Card
                key={order.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-4">
                      <img
                        src={
                          order.restaurant?.logo ||
                          "/placeholder.svg?height=60&width=60"
                        }
                        alt={order.restaurant?.name || "Restaurant"}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="font-semibold text-lg">
                          {order.restaurant?.name || "Restaurant"}
                        </h3>
                        <p className="text-gray-600">Order #{order.id}</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(order.created_at)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={getStatusBadgeVariant(order.status)}
                        className="mb-2"
                      >
                        {getStatusIcon(order.status)}
                        <span className="ml-1 capitalize">
                          {order.status.replace("_", " ")}
                        </span>
                      </Badge>
                      <p className="font-bold text-lg">
                        MK {order.total_amount}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.payment_method} •{" "}
                        {order.is_paid ? "Paid" : "Unpaid"}
                      </p>
                    </div>
                  </div>

                  {/* Order Items Preview */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Items:</p>
                    <div className="flex flex-wrap gap-2">
                      {order.items?.slice(0, 3).map((item, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {item.quantity}x{" "}
                          {item.name || `Item #${item.menu_item}`}
                        </Badge>
                      ))}
                      {order.items?.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{order.items.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Delivery Info */}
                  <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
                    <MapPin className="h-4 w-4" />
                    <span>{order.delivery_address}</span>
                  </div>

                  {/* Estimated Delivery Time for Active Orders */}
                  {[
                    "pending",
                    "confirmed",
                    "preparing",
                    "on_delivery",
                  ].includes(order.status) &&
                    order.estimated_delivery && (
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                        <div className="flex items-center gap-2 text-orange-800">
                          <Clock className="h-4 w-4" />
                          <span className="font-medium">
                            Estimated delivery:{" "}
                            {formatDate(order.estimated_delivery)}
                          </span>
                        </div>
                      </div>
                    )}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedOrder(order)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Order #{order.id} Details</DialogTitle>
                        </DialogHeader>
                        {selectedOrder && (
                          <CustomerOrderDetailsModal order={selectedOrder} />
                        )}
                      </DialogContent>
                    </Dialog>

                    {order.status === "delivered" && (
                      <Button size="sm" variant="outline">
                        <Star className="h-4 w-4 mr-1" />
                        Rate Order
                      </Button>
                    )}

                    {order.status === "delivered" && (
                      <Button size="sm">Order Again</Button>
                    )}

                    {order.status === "pending" && (
                      <Button size="sm" variant="destructive">
                        Cancel Order
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Customer Order Details Modal Component
function CustomerOrderDetailsModal({ order }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "pending":
        return "secondary";
      case "confirmed":
        return "default";
      case "preparing":
        return "outline";
      case "on_delivery":
        return "default";
      case "delivered":
        return "default";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "confirmed":
        return <CheckCircle className="h-4 w-4" />;
      case "preparing":
        return <Package className="h-4 w-4" />;
      case "on_delivery":
        return <Truck className="h-4 w-4" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4" />;
      case "cancelled":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-bold">Order #{order.id}</h2>
          <p className="text-gray-600">
            from {order.restaurant?.name || "Restaurant"}
          </p>
          <p className="text-sm text-gray-500">
            Placed on {formatDate(order.created_at)}
          </p>
        </div>
        <Badge
          variant={getStatusBadgeVariant(order.status)}
          className="text-sm"
        >
          {getStatusIcon(order.status)}
          <span className="ml-1">
            {order.status.replace("_", " ").toUpperCase()}
          </span>
        </Badge>
      </div>

      {/* Order Status Timeline */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3">Order Status</h3>
        <div className="space-y-3">
          <div
            className={`flex items-center gap-3 ${
              order.status === "pending" ? "text-orange-600" : "text-gray-400"
            }`}
          >
            <Clock className="h-4 w-4" />
            <span>Order Placed</span>
            {order.status !== "pending" && (
              <span className="text-green-600">✓</span>
            )}
          </div>
          <div
            className={`flex items-center gap-3 ${
              order.status === "confirmed"
                ? "text-orange-600"
                : order.status === "pending"
                ? "text-gray-400"
                : "text-gray-400"
            }`}
          >
            <CheckCircle className="h-4 w-4" />
            <span>Order Confirmed</span>
            {["confirmed", "preparing", "on_delivery", "delivered"].includes(
              order.status
            ) && <span className="text-green-600">✓</span>}
          </div>
          <div
            className={`flex items-center gap-3 ${
              order.status === "preparing"
                ? "text-orange-600"
                : ["pending", "confirmed"].includes(order.status)
                ? "text-gray-400"
                : "text-gray-400"
            }`}
          >
            <Package className="h-4 w-4" />
            <span>Preparing</span>
            {["preparing", "on_delivery", "delivered"].includes(
              order.status
            ) && <span className="text-green-600">✓</span>}
          </div>
          <div
            className={`flex items-center gap-3 ${
              order.status === "on_delivery"
                ? "text-orange-600"
                : ["pending", "confirmed", "preparing"].includes(order.status)
                ? "text-gray-400"
                : "text-gray-400"
            }`}
          >
            <Truck className="h-4 w-4" />
            <span>On Delivery</span>
            {["on_delivery", "delivered"].includes(order.status) && (
              <span className="text-green-600">✓</span>
            )}
          </div>
          <div
            className={`flex items-center gap-3 ${
              order.status === "delivered" ? "text-green-600" : "text-gray-400"
            }`}
          >
            <CheckCircle className="h-4 w-4" />
            <span>Delivered</span>
            {order.status === "delivered" && (
              <span className="text-green-600">✓</span>
            )}
          </div>
        </div>
      </div>

      {/* Delivery Information */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Delivery Information
        </h3>
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-600">Address</p>
            <p className="font-medium">{order.delivery_address}</p>
          </div>
          {order.delivery_instructions && (
            <div>
              <p className="text-sm text-gray-600">Special Instructions</p>
              <p className="font-medium">{order.delivery_instructions}</p>
            </div>
          )}
          {order.estimated_delivery && (
            <div>
              <p className="text-sm text-gray-600">Estimated Delivery</p>
              <p className="font-medium">
                {formatDate(order.estimated_delivery)}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Order Items */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Package className="h-4 w-4" />
          Order Items
        </h3>
        <div className="space-y-3">
          {order.items?.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center py-2 border-b last:border-b-0"
            >
              <div>
                <p className="font-medium">
                  {item.name || `Menu Item #${item.menu_item}`}
                </p>
                <p className="text-sm text-gray-600">
                  Quantity: {item.quantity}
                </p>
                {item.special_instructions && (
                  <p className="text-sm text-gray-600">
                    Note: {item.special_instructions}
                  </p>
                )}
              </div>
              <p className="font-semibold">MK {item.price}</p>
            </div>
          ))}
          <div className="flex justify-between items-center pt-3 border-t font-bold text-lg">
            <span>Total</span>
            <span>MK {order.total_amount}</span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          Payment Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Payment Method</p>
            <p className="font-medium capitalize">{order.payment_method}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Payment Status</p>
            <Badge variant={order.is_paid ? "default" : "secondary"}>
              {order.is_paid ? "Paid" : "Unpaid"}
            </Badge>
          </div>
          {order.payment && (
            <>
              <div>
                <p className="text-sm text-gray-600">Transaction ID</p>
                <p className="font-medium">{order.payment.transaction_id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Payment Date</p>
                <p className="font-medium">
                  {formatDate(order.payment.created_at)}
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        {order.status === "delivered" && (
          <>
            <Button className="flex-1">
              <Star className="h-4 w-4 mr-1" />
              Rate Order
            </Button>
            <Button variant="outline" className="flex-1">
              Order Again
            </Button>
          </>
        )}
        {order.status === "pending" && (
          <Button variant="destructive" className="flex-1">
            Cancel Order
          </Button>
        )}
        {["confirmed", "preparing", "on_delivery"].includes(order.status) && (
          <Button variant="outline" className="flex-1">
            <Phone className="h-4 w-4 mr-1" />
            Contact Restaurant
          </Button>
        )}
      </div>
    </div>
  );
}
