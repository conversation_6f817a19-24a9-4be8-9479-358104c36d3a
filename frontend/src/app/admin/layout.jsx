import DashboardLayout from "@/components/dashboardLayout";
import { FileCheck, FileUser, Home, Settings, Users } from "lucide-react";

const adminMenu = [
  {
    title: "Dashboard",
    url: "/admin/home",
    icon: Home,
  },
  {
    title: "Users",
    url: "/admin/users",
    icon: Users,
  },
  {
    title: "Requests",
    url: "/admin/requests",
    icon: FileCheck,
  },
  {
    title: "Restaurants",
    url: "/admin/restaurants",
    icon: FileUser,
  },
];

export default function AdminLayout({ children }) {
  return (
    <DashboardLayout sidebarItems={adminMenu} role="Admin">
      {children}
    </DashboardLayout>
  );
}
