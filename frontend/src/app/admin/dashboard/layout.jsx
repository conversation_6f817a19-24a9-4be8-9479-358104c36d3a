import DashboardLayout from "@/components/dashboardLayout";
import { FileCheck, FileUser, Home, Settings, Users } from "lucide-react";

const adminMenu = [
  {
    title: "Dashboard",
    url: "/admin/dashboard/home",
    icon: Home,
  },
  {
    title: "Users",
    url: "/admin/dashboard/users",
    icon: Users,
  },
  {
    title: "Requests",
    url: "/admin/dashboard/requests",
    icon: FileCheck,
  },
  {
    title: "Restaurants",
    url: "/admin/dashboard/restaurants",
    icon: FileUser,
  },
];

export default function AdminLayout({ children }) {
  return (
    <DashboardLayout sidebarItems={adminMenu} role="Admin">
      {children}
    </DashboardLayout>
  );
}
