import { DataTable } from "@/components/dataTable";
import { columns } from "./userColumns";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

async function getData() {
  const users = [
    {
      id: 1,
      name: "Chimwemwe Banda",
      email: "<EMAIL>",
      restaurant: "restaurant1",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      restaurant: "restaurant2",
    },
    {
      id: 3,
      name: "<PERSON><PERSON> Phiri",
      email: "<EMAIL>",
      restaurant: "restaurant3",
    },
    {
      id: 4,
      name: "Precious Ngwi<PERSON>",
      email: "<EMAIL>",
      restaurant: "restaurant4",
    },
    {
      id: 5,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      restaurant: "restaurant5",
    },
  ];

  return users;
}

export default async function UsersPage() {
  const data = await getData();

  return (
    <div className="container mx-auto py-10">
      <div>
        <div className="py-3 flex justify-start items-center flex-wrap gap-4">
          <Card className="max-w-xs">
            <CardHeader className="text-lg font-bold w-full text-center">Some Card</CardHeader>
            <CardContent>
              Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, adipisci!
            </CardContent>
          </Card>
          <Card className="max-w-xs">
            <CardHeader className="text-lg font-bold w-full text-center">Some other card</CardHeader>
            <CardContent>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium, esse.
            </CardContent>
          </Card>
          <Card className="max-w-xs">
            <CardHeader className="text-lg font-bold w-full text-center">Third card</CardHeader>
            <CardContent>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium, esse.
            </CardContent>
          </Card>
        </div>
        <Card >
          <CardHeader className="text-lg font-bold w-full text-center">Registration Requests</CardHeader>
          <CardContent>
            <DataTable filter={false} paginate={false} columns={columns} data={data} />
          </CardContent>
        </Card>
      </div>      
    </div>
  );
}
