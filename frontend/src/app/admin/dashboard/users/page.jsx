import { DataTable } from "@/components/dataTable";
import { columns } from "./colums";

async function getData() {
  const users = [
    {
      id: 1,
      name: "Chimwemwe Banda",
      email: "<EMAIL>",
      status: "active",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      status: "inactive",
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      status: "active",
    },
    {
      id: 4,
      name: "Precious Ngwira",
      email: "<EMAIL>",
      status: "inactive",
    },
    {
      id: 5,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      status: "active",
    },
  ];

  return users;
}

export default async function UsersPage() {
  const data = await getData();

  return (
    <div className="container mx-auto py-10">
      <div className="text-lg font-bold">Users</div>
      <DataTable columns={columns} data={data} />
    </div>
  );
}
