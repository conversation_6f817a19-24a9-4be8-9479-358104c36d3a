import { DataTable } from "@/components/dataTable";
import { columns } from "../home/<USER>";

async function getData() {
  const users = [
    {
      id: 1,
      name: "Chimwemwe Banda",
      email: "<EMAIL>",
      restaurant: "restrant1",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      restaurant: "restrant2",
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      restaurant: "restrant3",
    },
    {
      id: 4,
      name: "Precious Ngwira",
      email: "<EMAIL>",
      restaurant: "restrant4",
    },
    {
      id: 5,
      name: "<PERSON><PERSON><PERSON> Chirwa",
      email: "<EMAIL>",
      restaurant: "restrant5",
    },
  ];

  return users;
}

export default async function UsersPage() {
  const data = await getData();

  return (
    <div className="container mx-auto py-10">
      <div className="text-lg font-bold w-full text-center">Registration Requests</div>
      <DataTable columns={columns} data={data} />
    </div>
  );
}
