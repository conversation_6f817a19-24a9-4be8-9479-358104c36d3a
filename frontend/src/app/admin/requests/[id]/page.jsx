"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import {
  X,
  Building,
  User,
  Globe,
  MapPin,
  Phone,
  Mail,
  Calendar,
} from "lucide-react";
import Link from "next/link";

export default function RequestViewPage() {
  const [domain, setDomain] = useState("");
  const [editing, setEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [tempDomain, setTempDomain] = useState("");

  //sample request. Real request will be fetched from the backend
  const request = {
    //manager details
    managerFullName: "Samuel <PERSON>",
    managerEmail: "<EMAIL>",
    managerPhone: "099999999",
    //restaurant details
    restaurantName: "Pulp Fiction",
    restaurantPhone: "+265312375678",
    restaurantEmail: "<EMAIL>",
    restaurantGeneralLocation: "Some Area",
    restaurantLocation: "someCoordinates",
    //
    domain: "",
    submittedDate: "2025-08-10",
  };

  const handleAssignDomain = () => {
    setTempDomain(domain);
    setShowModal(true);
  };

  const handleSaveDomain = () => {
    setDomain(tempDomain);
    setEditing(true);
    setShowModal(false);
  };

  const handleSaveChanges = () => {
    setEditing(false);
    alert("Changes Saved Successfully!");
  };

  useEffect(() => {
    setDomain(request.domain);
  }, []);

  return (
    <>
      <div className="max-w-2xl mx-auto my-8 px-4">
        {/* Header */}
        <div className="text-gray-800 rounded-t-md p-6 shadow-lg border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">
                Restaurant Registration Request
              </h1>
              <p className="text-blue-400 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Submitted:{" "}
                {new Date(request.submittedDate).toLocaleDateString()}
              </p>
            </div>
            <div className="text-right">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                <span className="text-sm font-medium">Status</span>
                <p className="text-lg font-bold">Pending Review</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-b-xl shadow-lg overflow-hidden">
          <div className="p-6 space-y-8">
            {/* Manager Details Section */}
            <div className="border-l-4 border-blue-500 pl-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <User className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Manager Details
                </h2>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      Full Name
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.managerFullName}
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Phone
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.managerPhone}
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    Email
                  </label>
                  <p className="text-lg font-medium text-gray-900 mt-1 break-all">
                    {request.managerEmail}
                  </p>
                </div>
              </div>
            </div>

            {/* Restaurant Details Section */}
            <div className="border-l-4 border-green-500 pl-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-green-100 p-2 rounded-lg">
                  <Building className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Restaurant Details
                </h2>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      Restaurant Name
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.restaurantName}
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Phone
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.restaurantPhone}
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      General Location
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.restaurantGeneralLocation}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1 break-all">
                      {request.restaurantEmail}
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      Precise Location
                    </label>
                    <p className="text-lg font-medium text-gray-900 mt-1">
                      {request.restaurantLocation}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Domain Section */}
            <div className="border-l-4 border-purple-500 pl-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Globe className="w-5 h-5 text-purple-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Domain Assignment
                </h2>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  Domain Name
                </label>
                <p className="text-lg font-medium text-gray-900 mt-1">
                  {domain ? (
                    <Link
                      href={`https://${domain}`}
                      target="_blank"
                      className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {domain}
                    </Link>
                  ) : (
                    <span className="text-gray-400 italic">Not assigned</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Actions Section */}
          <div className="bg-gray-50 px-6 py-4 border-t">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-800">Actions</h3>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={handleAssignDomain}
                  className="flex items-center gap-2"
                >
                  <Globe className="w-4 h-4" />
                  {domain ? "Update Domain" : "Assign Domain"}
                </Button>
                {editing && (
                  <Button
                    onClick={handleSaveChanges}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    Save Changes
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Domain Assignment Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-800">
                Assign Domain
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Domain Name
                </label>
                <input
                  type="text"
                  value={tempDomain}
                  onChange={(e) => setTempDomain(e.target.value)}
                  placeholder="e.g., pulpfiction.example.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition-colors"
                />
              </div>

              <div className="text-sm text-gray-600 mb-6">
                <p>
                  Enter the domain name that will be assigned to this
                  restaurant.
                </p>
              </div>
            </div>

            <div className="flex gap-3 p-6 border-t bg-gray-50 rounded-b-xl">
              <Button
                variant="outline"
                onClick={() => setShowModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveDomain}
                disabled={!tempDomain.trim()}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50"
              >
                Assign Domain
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
