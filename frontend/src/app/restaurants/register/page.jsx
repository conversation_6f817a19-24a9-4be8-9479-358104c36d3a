"use client";

import React, { useState, useEffect, useRef } from "react";
import { toast, Toaster } from "sonner";

export default function RestaurantRegistration() {
  const [formData, setFormData] = useState({
    restaurant_name: "",
    manager_email: "",
    manager_firstname: "",
    manager_lastname: "",
    manager_phone: "",
    restaurant_phone: "",
    longitude: "",
    latitude: "",
    description: "",
    address: "",
    logo: null,
    banner: null,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [map, setMap] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);
  const mapRef = useRef(null);
  const markerRef = useRef(null);

  // Initialize map
  useEffect(() => {
    // Load Leaflet CSS and JS
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/leaflet.css";
    document.head.appendChild(link);

    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/leaflet.js";
    script.onload = () => {
      initializeMap();
    };
    document.head.appendChild(script);

    return () => {
      if (map) {
        map.remove();
      }
    };
  }, []);

  const initializeMap = () => {
    if (!mapRef.current || map) return;

    // Default to Lilongwe, Malawi
    const defaultLat = -13.9626;
    const defaultLng = 33.7741;

    const newMap = window.L.map(mapRef.current).setView(
      [defaultLat, defaultLng],
      12
    );

    window.L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(newMap);

    // Try to get user's location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          newMap.setView([userLat, userLng], 14);
          toast.success("Location detected", {
            description: "Map centered on your current location",
            duration: 3000,
          });
        },
        (error) => {
          toast.info("Using default location", {
            description: "Click on the map to select your restaurant location",
            duration: 4000,
          });
        }
      );
    }

    // Add click handler to place marker
    newMap.on("click", (e) => {
      const { lat, lng } = e.latlng;

      // Remove existing marker if it exists
      if (markerRef.current) {
        newMap.removeLayer(markerRef.current);
      }

      // Add new marker
      const newMarker = window.L.marker([lat, lng]).addTo(newMap);
      markerRef.current = newMarker;

      // Update form data
      setFormData((prev) => ({
        ...prev,
        latitude: lat.toString(),
        longitude: lng.toString(),
      }));

      toast.success("Location selected", {
        description: `Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
        duration: 3000,
      });
    });

    setMap(newMap);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      const file = files[0];

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Invalid file type", {
          description: "Please upload an image file (PNG, JPG, etc.)",
          duration: 4000,
        });
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File too large", {
          description: "Please upload an image smaller than 5MB",
          duration: 4000,
        });
        return;
      }

      setFormData((prev) => ({
        ...prev,
        [name]: file,
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (name === "logo") {
          setLogoPreview(e.target.result);
        } else if (name === "banner") {
          setBannerPreview(e.target.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate that location has been selected
      if (!formData.latitude || !formData.longitude) {
        toast.error("Location required", {
          description:
            "Please click on the map to select your restaurant location",
          duration: 4000,
        });
        setIsSubmitting(false);
        return;
      }

      // Create FormData for multipart upload
      const submitData = new FormData();

      // Add text fields
      Object.keys(formData).forEach((key) => {
        if (key === "logo" || key === "banner") {
          if (formData[key]) {
            submitData.append(key, formData[key]);
          }
        } else if (key === "longitude" || key === "latitude") {
          submitData.append(key, parseFloat(formData[key]) || 0.0);
        } else {
          submitData.append(key, formData[key]);
        }
      });

      const response = await fetch(
        "http://localhost:8000/api/public/register-restaurant/",
        {
          method: "POST",
          body: submitData, // Don't set Content-Type header for FormData
        }
      );

      const responseData = await response.json();

      if (response.ok) {
        toast.success("Restaurant registered successfully!", {
          description:
            "Welcome to DinePulse! You should receive a confirmation email shortly.",
          duration: 5000,
        });

        // Reset form
        setFormData({
          restaurant_name: "",
          manager_email: "",
          manager_firstname: "",
          manager_lastname: "",
          manager_phone: "",
          restaurant_phone: "",
          longitude: "",
          latitude: "",
          description: "",
          address: "",
          logo: null,
          banner: null,
        });
        setLogoPreview(null);
        setBannerPreview(null);
        if (markerRef.current) {
          map.removeLayer(markerRef.current);
          markerRef.current = null;
        }
      } else {
        // Handle validation errors
        if (responseData && typeof responseData === "object") {
          Object.entries(responseData).forEach(([field, errors]) => {
            if (Array.isArray(errors)) {
              errors.forEach((error) => {
                toast.error(`${field.replace("_", " ")}: ${error}`, {
                  duration: 4000,
                });
              });
            } else {
              toast.error(`${field.replace("_", " ")}: ${errors}`, {
                duration: 4000,
              });
            }
          });
        } else {
          toast.error("Registration failed", {
            description: "Please check your information and try again.",
            duration: 4000,
          });
        }
      }
    } catch (error) {
      toast.error("Network error", {
        description: "Unable to connect to the server. Please try again later.",
        duration: 4000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 py-12 px-4 sm:px-6 lg:px-8">
      <Toaster position="top-right" richColors />

      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-red-600 to-orange-500 bg-clip-text text-transparent mb-2">
            DinePulse
          </h1>
          <p className="text-xl text-gray-700 mb-2">Restaurant Registration</p>
          <p className="text-gray-600">
            Join thousands of restaurants already using DinePulse
          </p>
        </div>

        <div className="bg-white shadow-2xl rounded-2xl p-8 border border-red-100">
          <div className="space-y-6">
            {/* Restaurant Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-2 h-6 bg-gradient-to-b from-red-500 to-orange-500 rounded-full mr-3"></div>
                Restaurant Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label
                    htmlFor="restaurant_name"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Restaurant Name *
                  </label>
                  <input
                    type="text"
                    id="restaurant_name"
                    name="restaurant_name"
                    value={formData.restaurant_name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
                    placeholder="Enter your restaurant name"
                  />
                </div>

                <div>
                  <label
                    htmlFor="restaurant_phone"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Restaurant Phone *
                  </label>
                  <input
                    type="tel"
                    id="restaurant_phone"
                    name="restaurant_phone"
                    value={formData.restaurant_phone}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
                    placeholder="Restaurant phone number"
                  />
                </div>

                <div>
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Address *
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
                    placeholder="Restaurant address"
                  />
                </div>

                <div className="md:col-span-2">
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors resize-none"
                    placeholder="Brief description of your restaurant"
                  />
                </div>
              </div>
            </div>

            {/* Brand Assets */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-2 h-6 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-3"></div>
                Brand Assets
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Logo Upload */}
                <div>
                  <label
                    htmlFor="logo"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Restaurant Logo
                  </label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg hover:border-red-400 transition-colors">
                      {logoPreview ? (
                        <div className="relative w-full h-full">
                          <img
                            src={logoPreview}
                            alt="Logo preview"
                            className="w-full h-full object-contain rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setFormData((prev) => ({ ...prev, logo: null }));
                              setLogoPreview(null);
                            }}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="text-center">
                          <svg
                            className="mx-auto h-8 w-8 text-gray-400"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <p className="mt-2 text-sm text-gray-500">
                            <span className="font-medium text-red-600 hover:text-red-500">
                              Click to upload
                            </span>{" "}
                            or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">
                            PNG, JPG up to 5MB
                          </p>
                        </div>
                      )}
                    </div>
                    <input
                      id="logo"
                      name="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <button
                      type="button"
                      onClick={() => document.getElementById("logo").click()}
                      className="w-full px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                    >
                      Choose Logo File
                    </button>
                  </div>
                </div>

                {/* Banner Upload */}
                <div>
                  <label
                    htmlFor="banner"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Restaurant Banner
                  </label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg hover:border-red-400 transition-colors">
                      {bannerPreview ? (
                        <div className="relative w-full h-full">
                          <img
                            src={bannerPreview}
                            alt="Banner preview"
                            className="w-full h-full object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setFormData((prev) => ({
                                ...prev,
                                banner: null,
                              }));
                              setBannerPreview(null);
                            }}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="text-center">
                          <svg
                            className="mx-auto h-8 w-8 text-gray-400"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <p className="mt-2 text-sm text-gray-500">
                            <span className="font-medium text-red-600 hover:text-red-500">
                              Click to upload
                            </span>{" "}
                            or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">
                            PNG, JPG up to 5MB
                          </p>
                        </div>
                      )}
                    </div>
                    <input
                      id="banner"
                      name="banner"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <button
                      type="button"
                      onClick={() => document.getElementById("banner").click()}
                      className="w-full px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                    >
                      Choose Banner File
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Manager Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-2 h-6 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-3"></div>
                Manager Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="manager_firstname"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="manager_firstname"
                    name="manager_firstname"
                    value={formData.manager_firstname}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                    placeholder="Manager's first name"
                  />
                </div>

                <div>
                  <label
                    htmlFor="manager_lastname"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="manager_lastname"
                    name="manager_lastname"
                    value={formData.manager_lastname}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                    placeholder="Manager's last name"
                  />
                </div>

                <div>
                  <label
                    htmlFor="manager_email"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="manager_email"
                    name="manager_email"
                    value={formData.manager_email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label
                    htmlFor="manager_phone"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="manager_phone"
                    name="manager_phone"
                    value={formData.manager_phone}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                    placeholder="Manager's phone number"
                  />
                </div>
              </div>
            </div>

            {/* Location Information */}
            <div className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-2 h-6 bg-gradient-to-b from-red-400 to-orange-400 rounded-full mr-3"></div>
                Restaurant Location *
              </h3>

              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-3">
                  Click on the map to select your restaurant's exact location
                </p>
                <div
                  ref={mapRef}
                  className="w-full h-64 border border-gray-300 rounded-lg"
                  style={{ minHeight: "300px" }}
                ></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="latitude"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Latitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    id="latitude"
                    name="latitude"
                    value={formData.latitude}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-400 focus:border-transparent transition-colors bg-gray-50"
                    placeholder="Click on map to set"
                    readOnly
                  />
                </div>

                <div>
                  <label
                    htmlFor="longitude"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Longitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    id="longitude"
                    name="longitude"
                    value={formData.longitude}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-400 focus:border-transparent transition-colors bg-gray-50"
                    placeholder="Click on map to set"
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <button
                type="submit"
                disabled={isSubmitting}
                onClick={handleSubmit}
                className="w-full bg-gradient-to-r from-red-600 to-orange-500 text-white font-semibold py-4 px-6 rounded-lg hover:from-red-700 hover:to-orange-600 focus:ring-4 focus:ring-red-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Registering Restaurant...
                  </div>
                ) : (
                  "Register Restaurant"
                )}
              </button>
            </div>
          </div>

          <div className="mt-6 text-center text-sm text-gray-500">
            Already have an account?{" "}
            <a
              href="/auth/login"
              className="text-red-600 hover:text-red-700 font-medium transition-colors"
            >
              Login here
            </a>
          </div>
        </div>

        <div className="mt-8 text-center text-sm text-gray-500">
          By registering, you agree to our{" "}
          <a
            href="#"
            className="text-red-600 hover:text-red-700 transition-colors"
          >
            Terms of Service
          </a>{" "}
          and{" "}
          <a
            href="#"
            className="text-red-600 hover:text-red-700 transition-colors"
          >
            Privacy Policy
          </a>
        </div>
      </div>
    </div>
  );
}
