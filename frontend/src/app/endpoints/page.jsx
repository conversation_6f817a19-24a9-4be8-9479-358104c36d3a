export default function EndpointsPage() {
  return (
    <main className="max-w-5xl mx-auto p-6 space-y-6">
      <h1 className="text-4xl font-bold">
        Easy Eats API Endpoints Documentation
      </h1>

      <p>
        This document provides a comprehensive overview of all API endpoints in
        the Easy Eats backend system.
      </p>

      {/* Base URLs */}
      <section>
        <h2 className="text-2xl font-semibold">Base URLs</h2>
        <p>The system uses Django Tenants with different URL patterns:</p>
        <ul className="list-disc list-inside">
          <li>
            <strong>Public Domain</strong>: For customer registration and
            restaurant registration
          </li>
          <li>
            <strong>Tenant Domains</strong>: For restaurant-specific operations
            (each restaurant has its own subdomain)
          </li>
        </ul>
      </section>

      {/* Authentication */}
      <section>
        <h2 className="text-2xl font-semibold">Authentication</h2>
        <p>The system uses multiple authentication methods:</p>
        <ul className="list-disc list-inside">
          <li>
            <strong>Token Authentication</strong>: For manager authentication
          </li>
          <li>
            <strong>JWT Authentication</strong>: For customer authentication
          </li>
          <li>
            <strong>Session Authentication</strong>: For admin operations
          </li>
        </ul>
      </section>

      <hr className="my-6" />

      {/* Public API Endpoints */}
      <section>
        <h2 className="text-3xl font-bold">Public API Endpoints</h2>

        {/* Restaurant Registration */}
        <article className="space-y-4">
          <h3 className="text-xl font-semibold">Restaurant Registration</h3>
          <p>
            <strong>Endpoint:</strong>{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">
              POST /api/public/register-restaurant/
            </code>
          </p>
          <p>
            <strong>Description:</strong> Register a new restaurant (creates a
            pending client)
          </p>
          <p>
            <strong>Authentication:</strong> None required
          </p>

          <div>
            <p className="font-semibold">Input:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "restaurant_name": "string (required)",
  "manager_email": "email (required)",
  "manager_firstname": "string (required)",
  "manager_lastname": "string (required)",
  "manager_phone": "string (required)",
  "restaurant_phone": "string (required)",
  "longitude": "float (required)",
  "latitude": "float (required)",
  "description": "string (optional)",
  "address": "string (required)",
  "logo": "image file (optional)",
  "banner": "image file (optional)"
}`}</pre>
          </div>

          <div>
            <p className="font-semibold">Output:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "id": "integer",
  "restaurant_name": "string",
  "schema_name": "string (auto-generated)",
  "manager_email": "email",
  "manager_firstname": "string",
  "manager_lastname": "string",
  "manager_phone": "string",
  "restaurant_phone": "string",
  "longitude": "float",
  "latitude": "float",
  "description": "string",
  "address": "string",
  "logo": "url",
  "banner": "url"
}`}</pre>
          </div>
        </article>

        {/* Manager Authentication */}
        <article className="space-y-4">
          <h3 className="text-xl font-semibold">Manager Authentication</h3>
          <p>
            <strong>Endpoint:</strong>{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">
              POST /api/public/manager-auth/
            </code>
          </p>
          <p>
            <strong>Description:</strong> Authenticate restaurant managers using
            restaurant_id
          </p>
          <p>
            <strong>Authentication:</strong> None required
          </p>

          <div>
            <p className="font-semibold">Input:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "restaurant_id": "string (required) - schema name of restaurant",
  "username": "string (required)",
  "password": "string (required)"
}`}</pre>
          </div>

          <div>
            <p className="font-semibold">Output:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "token": "string",
  "user_id": "integer",
  "email": "string",
  "restaurant_name": "string",
  "schema_name": "string"
}`}</pre>
          </div>

          <div>
            <p className="font-semibold">Error Responses:</p>
            <ul className="list-disc list-inside">
              <li>400: Missing required fields</li>
              <li>404: Restaurant not found</li>
              <li>401: Invalid credentials or user is not a manager</li>
            </ul>
          </div>
        </article>

        {/* Customer Registration */}
        <article className="space-y-4">
          <h3 className="text-xl font-semibold">Customer Registration</h3>
          <p>
            <strong>Endpoint:</strong>{" "}
            <code className="bg-gray-100 px-2 py-1 rounded">
              POST /api/public/customer/register/
            </code>
          </p>
          <p>
            <strong>Description:</strong> Register a new customer
          </p>
          <p>
            <strong>Authentication:</strong> None required
          </p>

          <div>
            <p className="font-semibold">Input:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "email": "email (required)",
  "username": "string (required)",
  "password": "string (required)",
  "confirm_password": "string (required)",
  "phone_number": "string (optional)"
}`}</pre>
          </div>

          <div>
            <p className="font-semibold">Output:</p>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">{`{
  "message": "Customer registered successfully"
}`}</pre>
          </div>

          <p className="font-semibold">Error Responses:</p>
          <ul className="list-disc list-inside">
            <li>
              400: Validation errors (password mismatch, existing
              email/username)
            </li>
          </ul>
        </article>
        {/* TODO: continue with the endpoints thing */}
      </section>
    </main>
  );
}
