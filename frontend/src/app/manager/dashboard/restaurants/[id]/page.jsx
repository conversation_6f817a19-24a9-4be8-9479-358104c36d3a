"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  MapPin,
  Users,
  Star,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  Phone,
  Mail,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import MapDisplay from "@/components/map-display";

export default function RestaurantDetailsPage() {
  const params = useParams();
  const [activeTab, setActiveTab] = useState("dishes");
  const [isAddDishOpen, setIsAddDishOpen] = useState(false);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);

  const restaurant = {
    id: params.id,
    name: "Blantyre Delights",
    location: "Some Highway, Blantyre",
    coordinates: [-15.78499, 35.00854],
    status: "Active",
    staff: 15,
    rating: 4.8,
    revenue: "MWK12,450",
    image: "/placeholder.svg?height=300&width=500",
  };

  const [dishes, setDishes] = useState([
    {
      id: 1,
      name: "Grilled Salmon",
      category: "Main Course",
      price: "MWK24.99",
      status: "Available",
    },
    {
      id: 2,
      name: "Caesar Salad",
      category: "Appetizer",
      price: "MWK12.99",
      status: "Available",
    },
    {
      id: 3,
      name: "Chocolate Cake",
      category: "Dessert",
      price: "MWK8.99",
      status: "Out of Stock",
    },
    {
      id: 4,
      name: "Beef Burger",
      category: "Main Course",
      price: "MWK16.99",
      status: "Available",
    },
  ]);

  const [users, setUsers] = useState([
    {
      id: 1,
      name: "Chimwemwe Banda",
      role: "Branch Manager",
      email: "<EMAIL>",
      phone: "+265881234567",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      name: "Thoko Phiri",
      role: "Waiter",
      email: "<EMAIL>",
      phone: "+265882345678",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      name: "Mphatso Nkhoma",
      role: "Delivery Personnel",
      email: "<EMAIL>",
      phone: "+265883456789",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 4,
      name: "Liness Chirwa",
      role: "Waiter",
      email: "<EMAIL>",
      phone: "+265884567890",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]);

  const [newDish, setNewDish] = useState({
    name: "",
    category: "",
    price: "",
    description: "",
    status: "Available",
  });

  const [newUser, setNewUser] = useState({
    name: "",
    role: "",
    email: "",
    phone: "",
  });

  const handleAddDish = () => {
    if (newDish.name && newDish.category && newDish.price) {
      setDishes([
        ...dishes,
        {
          id: dishes.length + 1,
          ...newDish,
        },
      ]);
      setNewDish({
        name: "",
        category: "",
        price: "",
        description: "",
        status: "Available",
      });
      setIsAddDishOpen(false);
    }
  };

  const handleAddUser = () => {
    if (newUser.name && newUser.role && newUser.email) {
      setUsers([
        ...users,
        {
          id: users.length + 1,
          ...newUser,
          avatar: "/placeholder.svg?height=40&width=40",
        },
      ]);
      setNewUser({ name: "", role: "", email: "", phone: "" });
      setIsAddUserOpen(false);
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "Branch Manager":
        return "bg-purple-100 text-purple-800";
      case "Waiter":
        return "bg-blue-100 text-blue-800";
      case "Delivery Personnel":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Available":
        return "bg-green-100 text-green-800";
      case "Out of Stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/manager/dashboard/restaurants">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Restaurants
              </Button>
            </Link>
          </div>

          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col lg:flex-row gap-6">
                  <img
                    src={restaurant.image || "/placeholder.svg"}
                    alt={restaurant.name}
                    className="w-full lg:w-64 h-48 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                          {restaurant.name}
                        </h1>
                        <div className="flex items-center gap-2 text-gray-600 mb-2">
                          <MapPin className="h-4 w-4" />
                          {restaurant.location}
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {restaurant.status}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">Staff Members</p>
                          <p className="font-semibold">{restaurant.staff}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                        <div>
                          <p className="text-sm text-gray-600">Rating</p>
                          <p className="font-semibold">{restaurant.rating}/5</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Revenue</p>
                        <p className="font-semibold text-green-600">
                          {restaurant.revenue}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Map Display */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Restaurant Location
                  </h3>
                  <MapDisplay
                    position={restaurant.coordinates}
                    restaurantName={restaurant.name}
                    height="300px"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="dishes">Dishes & Menu</TabsTrigger>
            <TabsTrigger value="users">Staff Management</TabsTrigger>
            <TabsTrigger value="location">Location Management</TabsTrigger>
          </TabsList>

          {/* Dishes Tab */}
          <TabsContent value="dishes" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Menu Items</CardTitle>
                    <CardDescription>
                      Manage dishes and menu items for this restaurant
                    </CardDescription>
                  </div>
                  <Dialog open={isAddDishOpen} onOpenChange={setIsAddDishOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Dish
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Dish</DialogTitle>
                        <DialogDescription>
                          Add a new dish to the menu
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="dish-name">Dish Name</Label>
                          <Input
                            id="dish-name"
                            value={newDish.name}
                            onChange={(e) =>
                              setNewDish({ ...newDish, name: e.target.value })
                            }
                            placeholder="Enter dish name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="dish-category">Category</Label>
                          <Select
                            value={newDish.category}
                            onValueChange={(value) =>
                              setNewDish({ ...newDish, category: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Appetizer">
                                Appetizer
                              </SelectItem>
                              <SelectItem value="Main Course">
                                Main Course
                              </SelectItem>
                              <SelectItem value="Dessert">Dessert</SelectItem>
                              <SelectItem value="Beverage">Beverage</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="dish-price">Price</Label>
                          <Input
                            id="dish-price"
                            value={newDish.price}
                            onChange={(e) =>
                              setNewDish({ ...newDish, price: e.target.value })
                            }
                            placeholder="MWK0.00"
                          />
                        </div>
                        <div>
                          <Label htmlFor="dish-description">Description</Label>
                          <Textarea
                            id="dish-description"
                            value={newDish.description}
                            onChange={(e) =>
                              setNewDish({
                                ...newDish,
                                description: e.target.value,
                              })
                            }
                            placeholder="Enter dish description"
                          />
                        </div>
                        <Button onClick={handleAddDish} className="w-full">
                          Add Dish
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Dish Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dishes.map((dish) => (
                      <TableRow key={dish.id}>
                        <TableCell className="font-medium">
                          {dish.name}
                        </TableCell>
                        <TableCell>{dish.category}</TableCell>
                        <TableCell>{dish.price}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(dish.status)}>
                            {dish.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Staff Members</CardTitle>
                    <CardDescription>
                      Manage staff members for this restaurant
                    </CardDescription>
                  </div>
                  <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add Staff Member
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Staff Member</DialogTitle>
                        <DialogDescription>
                          Add a new staff member to this restaurant
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="user-name">Full Name</Label>
                          <Input
                            id="user-name"
                            value={newUser.name}
                            onChange={(e) =>
                              setNewUser({ ...newUser, name: e.target.value })
                            }
                            placeholder="Enter full name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="user-role">Role</Label>
                          <Select
                            value={newUser.role}
                            onValueChange={(value) =>
                              setNewUser({ ...newUser, role: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Branch Manager">
                                Branch Manager
                              </SelectItem>
                              <SelectItem value="Waiter">Waiter</SelectItem>
                              <SelectItem value="Delivery Personnel">
                                Delivery Personnel
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="user-email">Email</Label>
                          <Input
                            id="user-email"
                            type="email"
                            value={newUser.email}
                            onChange={(e) =>
                              setNewUser({ ...newUser, email: e.target.value })
                            }
                            placeholder="Enter email address"
                          />
                        </div>
                        <div>
                          <Label htmlFor="user-phone">Phone Number</Label>
                          <Input
                            id="user-phone"
                            value={newUser.phone}
                            onChange={(e) =>
                              setNewUser({ ...newUser, phone: e.target.value })
                            }
                            placeholder="Enter phone number"
                          />
                        </div>
                        <Button onClick={handleAddUser} className="w-full">
                          Add Staff Member
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Staff Member</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage
                                src={user.avatar || "/placeholder.svg"}
                              />
                              <AvatarFallback>
                                {user.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.name}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleColor(user.role)}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3" />
                              {user.email}
                            </div>
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              {user.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Location Management Tab */}
          <TabsContent value="location" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Restaurant Location Management
                </CardTitle>
                <CardDescription>
                  Manage your restaurant's location and address information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Current Location Display */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Current Location</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Address</Label>
                      <p className="text-sm text-gray-600 mt-1">
                        {restaurant.location}
                      </p>
                    </div>
                    <div>
                      <Label>Coordinates</Label>
                      <p className="text-sm text-gray-600 mt-1">
                        {restaurant.coordinates[0].toFixed(6)},{" "}
                        {restaurant.coordinates[1].toFixed(6)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Location Update Form */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold mb-4">
                    Update Location
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="address">Restaurant Address</Label>
                      <Textarea
                        id="address"
                        placeholder="Enter the full address of your restaurant"
                        defaultValue={restaurant.location}
                        className="mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="latitude">Latitude</Label>
                        <Input
                          id="latitude"
                          type="number"
                          step="any"
                          placeholder="e.g., -15.78499"
                          defaultValue={restaurant.coordinates[0]}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="longitude">Longitude</Label>
                        <Input
                          id="longitude"
                          type="number"
                          step="any"
                          placeholder="e.g., 35.00854"
                          defaultValue={restaurant.coordinates[1]}
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">
                        How to get coordinates:
                      </h4>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>
                          • Use Google Maps: Right-click on your location and
                          copy coordinates
                        </li>
                        <li>
                          • Use GPS apps on your phone at the restaurant
                          location
                        </li>
                        <li>• Search for your address on mapping services</li>
                      </ul>
                    </div>

                    <div className="flex gap-3">
                      <Button className="flex-1">Update Location</Button>
                      <Button variant="outline" className="flex-1">
                        Use Current Location
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Map Display */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold mb-4">
                    Location Preview
                  </h3>
                  <div className="bg-gray-100 rounded-lg p-4">
                    <p className="text-sm text-gray-600 mb-2">
                      Interactive map showing your restaurant location
                    </p>
                    <div className="bg-white rounded border h-64 flex items-center justify-center">
                      <p className="text-gray-500">
                        Map component would be displayed here
                      </p>
                    </div>
                  </div>
                </div>

                {/* Additional Location Settings */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold mb-4">
                    Delivery Settings
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="delivery-radius">
                        Delivery Radius (km)
                      </Label>
                      <Input
                        id="delivery-radius"
                        type="number"
                        placeholder="e.g., 10"
                        defaultValue="10"
                        className="mt-1 max-w-xs"
                      />
                      <p className="text-sm text-gray-600 mt-1">
                        Maximum distance for delivery from this location
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="delivery-fee">Delivery Fee (MWK)</Label>
                      <Input
                        id="delivery-fee"
                        type="number"
                        placeholder="e.g., 1000"
                        defaultValue="1000"
                        className="mt-1 max-w-xs"
                      />
                    </div>

                    <Button variant="outline">Update Delivery Settings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
