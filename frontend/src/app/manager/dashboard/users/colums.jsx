"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rash, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const getAuthHeaders = () => {
  const token =
    localStorage.getItem("managerToken") ||
    localStorage.getItem("accessToken") ||
    localStorage.getItem("adminToken");

  return {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
};

const getApiUrl = (endpoint) => {
  // Get the schema name from localStorage (stored during login)
  const schemaName = localStorage.getItem("schemaName");

  if (!schemaName) {
    console.error("No schema name found in localStorage");
    return null;
  }

  // Convert schema name to subdomain format (e.g., tahit_restaurant -> tahit-restaurant)
  const subdomain = schemaName.replace(/_/g, "-");

  // Use the tenant-specific subdomain URL
  const baseUrl = `http://${subdomain}.localhost:8000/`;

  return `${baseUrl}api/tenant/${endpoint}`;
};

const handleDeleteUser = async (userId, userName) => {
  if (
    !confirm(
      `Are you sure you want to delete ${userName}? This action cannot be undone.`
    )
  ) {
    return;
  }

  try {
    const apiUrl = getApiUrl(`staff/${userId}/`);
    if (!apiUrl) return;

    const response = await fetch(apiUrl, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });

    if (response.ok) {
      toast.success("Staff member deleted successfully");
      // Refresh the page to update the list
      window.location.reload();
    } else {
      console.error("Failed to delete user:", response.status);
      toast.error(`Failed to delete user: ${response.status}`);
    }
  } catch (error) {
    console.error("Network error deleting user:", error);
    toast.error("Network error");
  }
};

export const columns = [
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    filterable: true,
  },
  { accessorKey: "email", header: "Email", filterable: true },
  {
    accessorKey: "role",
    header: "Role",
    filterable: false,
    cell: ({ row }) => {
      const role = row.getValue("role");
      return <span className="capitalize">{role}</span>;
    },
  },
  {
    accessorKey: "username",
    header: "Username",
    filterable: false,
    cell: ({ row }) => {
      const username = row.getValue("username");
      return <span className="text-sm text-gray-600">{username}</span>;
    },
  },
  {
    accessorKey: "is_active",
    header: "Status",
    filterable: false,
    cell: ({ row }) => {
      const isActive = row.getValue("is_active");
      return (
        <Badge variant={isActive ? "default" : "secondary"}>
          {isActive ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => (window.location.href = `users/${user.id}`)}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteUser(user.id, user.name)}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
