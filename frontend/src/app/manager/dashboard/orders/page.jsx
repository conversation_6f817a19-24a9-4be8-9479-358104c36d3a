"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ShoppingCart,
  Search,
  Filter,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  DollarSign,
  User,
  MapPin,
  Phone,
  Calendar,
  Package,
} from "lucide-react";

export default function OrderManagement() {
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);

  // Get auth headers for API calls
  const getAuthHeaders = () => {
    const token = localStorage.getItem("managerToken");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Token ${token}` }),
    };
  };

  // Fetch orders from API
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          "http://localhost:8000/api/auth/orders/orders/",
          {
            headers: getAuthHeaders(),
          }
        );

        if (response.ok) {
          const data = await response.json();
          const ordersData = data.results || data;
          setOrders(ordersData);
          setFilteredOrders(ordersData);
        } else {
          // Fallback to mock data if API fails
          const mockOrders = [
            {
              id: 1,
              customer: {
                id: 1,
                username: "john_doe",
                email: "<EMAIL>",
                phone_number: "+265991234567",
              },
              status: "pending",
              created_at: "2024-01-15T10:30:00Z",
              updated_at: "2024-01-15T10:30:00Z",
              total_amount: "25.50",
              payment_method: "cash",
              is_paid: false,
              delivery_address: "123 Main St, Lilongwe",
              delivery_instructions: "Ring the bell twice",
              delivery_person: null,
              items: [
                {
                  id: 1,
                  menu_item: 1,
                  quantity: 2,
                  price: "12.75",
                  special_instructions: "Extra spicy",
                },
              ],
              payment: null,
            },
            {
              id: 2,
              customer: {
                id: 2,
                username: "jane_smith",
                email: "<EMAIL>",
                phone_number: "+265997654321",
              },
              status: "confirmed",
              created_at: "2024-01-15T09:15:00Z",
              updated_at: "2024-01-15T09:45:00Z",
              total_amount: "18.75",
              payment_method: "card",
              is_paid: true,
              delivery_address: "456 Oak Ave, Blantyre",
              delivery_instructions: "",
              delivery_person: {
                id: 3,
                username: "delivery1",
                email: "<EMAIL>",
              },
              items: [
                {
                  id: 2,
                  menu_item: 2,
                  quantity: 1,
                  price: "18.75",
                  special_instructions: "",
                },
              ],
              payment: {
                id: 1,
                amount: "18.75",
                transaction_id: "TXN123456",
                payment_method: "card",
                status: "completed",
                created_at: "2024-01-15T09:20:00Z",
              },
            },
          ];
          setOrders(mockOrders);
          setFilteredOrders(mockOrders);
        }
      } catch (error) {
        console.error("Error fetching orders:", error);
        // Use mock data on error
        setOrders([]);
        setFilteredOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Filter orders based on search and status
  useEffect(() => {
    let filtered = orders;

    if (searchQuery) {
      filtered = filtered.filter(
        (order) =>
          order.customer.username
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.customer.email
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.id.toString().includes(searchQuery)
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    setFilteredOrders(filtered);
  }, [searchQuery, statusFilter, orders]);

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await fetch(
        `http://localhost:8000/api/auth/orders/orders/${orderId}/`,
        {
          method: "PATCH",
          headers: getAuthHeaders(),
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (response.ok) {
        const updatedOrder = await response.json();
        setOrders(
          orders.map((order) =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder({ ...selectedOrder, status: newStatus });
        }
      } else {
        // Update locally if API fails
        setOrders(
          orders.map((order) =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder({ ...selectedOrder, status: newStatus });
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  // Get status badge color
  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "pending":
        return "secondary";
      case "confirmed":
        return "default";
      case "preparing":
        return "outline";
      case "on_delivery":
        return "default";
      case "delivered":
        return "default";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "confirmed":
        return <CheckCircle className="h-4 w-4" />;
      case "preparing":
        return <Package className="h-4 w-4" />;
      case "on_delivery":
        return <Truck className="h-4 w-4" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4" />;
      case "cancelled":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Calculate order statistics
  const orderStats = {
    total: orders.length,
    pending: orders.filter((o) => o.status === "pending").length,
    confirmed: orders.filter((o) => o.status === "confirmed").length,
    preparing: orders.filter((o) => o.status === "preparing").length,
    onDelivery: orders.filter((o) => o.status === "on_delivery").length,
    delivered: orders.filter((o) => o.status === "delivered").length,
    cancelled: orders.filter((o) => o.status === "cancelled").length,
    totalRevenue: orders
      .filter((o) => o.status === "delivered")
      .reduce((sum, o) => sum + parseFloat(o.total_amount), 0),
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Order Management</h1>
          <p className="text-gray-600">
            Manage and track all restaurant orders
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Orders
                </p>
                <p className="text-2xl font-bold">{orderStats.total}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-orange-600">
                  {orderStats.pending}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Delivered</p>
                <p className="text-2xl font-bold text-green-600">
                  {orderStats.delivered}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  MK {orderStats.totalRevenue.toFixed(2)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by customer name, email, or order ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Orders</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="preparing">Preparing</SelectItem>
                  <SelectItem value="on_delivery">On Delivery</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Orders ({filteredOrders.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No orders found
              </h3>
              <p className="text-gray-600">
                {searchQuery || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "Orders will appear here when customers place them"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredOrders.map((order) => (
                <div
                  key={order.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center gap-3">
                      <div>
                        <h3 className="font-semibold text-lg">
                          Order #{order.id}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <User className="h-4 w-4" />
                          <span>{order.customer.username}</span>
                          <span>•</span>
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(order.created_at)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(order.status)}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1 capitalize">
                          {order.status.replace("_", " ")}
                        </span>
                      </Badge>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedOrder(order)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Order #{order.id} Details</DialogTitle>
                          </DialogHeader>
                          {selectedOrder && (
                            <OrderDetailsModal
                              order={selectedOrder}
                              onStatusUpdate={updateOrderStatus}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Customer</p>
                      <p className="font-medium">{order.customer.email}</p>
                      {order.customer.phone_number && (
                        <p className="text-gray-600">
                          {order.customer.phone_number}
                        </p>
                      )}
                    </div>
                    <div>
                      <p className="text-gray-600">Delivery Address</p>
                      <p className="font-medium">{order.delivery_address}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Total Amount</p>
                      <p className="font-bold text-lg">
                        MK {order.total_amount}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.payment_method} •{" "}
                        {order.is_paid ? "Paid" : "Unpaid"}
                      </p>
                    </div>
                  </div>

                  {order.status === "pending" && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        onClick={() => updateOrderStatus(order.id, "confirmed")}
                      >
                        Confirm Order
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => updateOrderStatus(order.id, "cancelled")}
                      >
                        Cancel Order
                      </Button>
                    </div>
                  )}

                  {order.status === "confirmed" && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        onClick={() => updateOrderStatus(order.id, "preparing")}
                      >
                        Start Preparing
                      </Button>
                    </div>
                  )}

                  {order.status === "preparing" && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        onClick={() =>
                          updateOrderStatus(order.id, "on_delivery")
                        }
                      >
                        Ready for Delivery
                      </Button>
                    </div>
                  )}

                  {order.status === "on_delivery" && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        onClick={() => updateOrderStatus(order.id, "delivered")}
                      >
                        Mark as Delivered
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Order Details Modal Component
function OrderDetailsModal({ order, onStatusUpdate }) {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "pending":
        return "secondary";
      case "confirmed":
        return "default";
      case "preparing":
        return "outline";
      case "on_delivery":
        return "default";
      case "delivered":
        return "default";
      case "cancelled":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-bold">Order #{order.id}</h2>
          <p className="text-gray-600">
            Placed on {formatDate(order.created_at)}
          </p>
        </div>
        <Badge
          variant={getStatusBadgeVariant(order.status)}
          className="text-sm"
        >
          {order.status.replace("_", " ").toUpperCase()}
        </Badge>
      </div>

      {/* Customer Information */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <User className="h-4 w-4" />
          Customer Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Name</p>
            <p className="font-medium">{order.customer.username}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Email</p>
            <p className="font-medium">{order.customer.email}</p>
          </div>
          {order.customer.phone_number && (
            <div>
              <p className="text-sm text-gray-600">Phone</p>
              <p className="font-medium">{order.customer.phone_number}</p>
            </div>
          )}
        </div>
      </div>

      {/* Delivery Information */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Delivery Information
        </h3>
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-600">Address</p>
            <p className="font-medium">{order.delivery_address}</p>
          </div>
          {order.delivery_instructions && (
            <div>
              <p className="text-sm text-gray-600">Special Instructions</p>
              <p className="font-medium">{order.delivery_instructions}</p>
            </div>
          )}
          {order.delivery_person && (
            <div>
              <p className="text-sm text-gray-600">Delivery Person</p>
              <p className="font-medium">{order.delivery_person.username}</p>
            </div>
          )}
        </div>
      </div>

      {/* Order Items */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Package className="h-4 w-4" />
          Order Items
        </h3>
        <div className="space-y-3">
          {order.items.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center py-2 border-b last:border-b-0"
            >
              <div>
                <p className="font-medium">Menu Item #{item.menu_item}</p>
                <p className="text-sm text-gray-600">
                  Quantity: {item.quantity}
                </p>
                {item.special_instructions && (
                  <p className="text-sm text-gray-600">
                    Note: {item.special_instructions}
                  </p>
                )}
              </div>
              <p className="font-semibold">MK {item.price}</p>
            </div>
          ))}
          <div className="flex justify-between items-center pt-3 border-t font-bold text-lg">
            <span>Total</span>
            <span>MK {order.total_amount}</span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          Payment Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Payment Method</p>
            <p className="font-medium capitalize">{order.payment_method}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Payment Status</p>
            <Badge variant={order.is_paid ? "default" : "secondary"}>
              {order.is_paid ? "Paid" : "Unpaid"}
            </Badge>
          </div>
          {order.payment && (
            <>
              <div>
                <p className="text-sm text-gray-600">Transaction ID</p>
                <p className="font-medium">{order.payment.transaction_id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Payment Date</p>
                <p className="font-medium">
                  {formatDate(order.payment.created_at)}
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Status Update Actions */}
      <div className="border rounded-lg p-4">
        <h3 className="font-semibold mb-3">Update Order Status</h3>
        <div className="flex flex-wrap gap-2">
          {order.status === "pending" && (
            <>
              <Button
                size="sm"
                onClick={() => onStatusUpdate(order.id, "confirmed")}
              >
                Confirm Order
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => onStatusUpdate(order.id, "cancelled")}
              >
                Cancel Order
              </Button>
            </>
          )}
          {order.status === "confirmed" && (
            <Button
              size="sm"
              onClick={() => onStatusUpdate(order.id, "preparing")}
            >
              Start Preparing
            </Button>
          )}
          {order.status === "preparing" && (
            <Button
              size="sm"
              onClick={() => onStatusUpdate(order.id, "on_delivery")}
            >
              Ready for Delivery
            </Button>
          )}
          {order.status === "on_delivery" && (
            <Button
              size="sm"
              onClick={() => onStatusUpdate(order.id, "delivered")}
            >
              Mark as Delivered
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
