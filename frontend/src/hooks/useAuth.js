/**
 * Authentication hook for managing user state across the application
 */

import { useState, useEffect, useCallback } from 'react';
import apiService from '@/lib/api';

export function useAuth(userType = 'customer') {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, [userType]);

  const checkAuthStatus = useCallback(() => {
    setLoading(true);
    
    const isAuth = apiService.isAuthenticated(userType);
    setIsAuthenticated(isAuth);
    
    if (isAuth) {
      const userData = apiService.getCurrentUser(userType);
      setUser(userData);
    } else {
      setUser(null);
    }
    
    setLoading(false);
  }, [userType]);

  const login = useCallback(async (credentials, restaurantId = null) => {
    try {
      let response;
      
      if (userType === 'manager' && restaurantId) {
        response = await apiService.managerLogin(restaurantId, credentials);
      } else if (userType === 'customer') {
        response = await apiService.customerLogin(credentials);
      } else {
        throw new Error('Invalid login parameters');
      }
      
      // Update state
      checkAuthStatus();
      
      return response;
    } catch (error) {
      throw error;
    }
  }, [userType, checkAuthStatus]);

  const logout = useCallback(() => {
    apiService.logout(userType);
    setUser(null);
    setIsAuthenticated(false);
  }, [userType]);

  const refreshToken = useCallback(async (restaurantId = null) => {
    try {
      if (userType === 'manager' && restaurantId) {
        await apiService.managerRefreshToken(restaurantId);
      } else if (userType === 'customer') {
        await apiService.customerRefreshToken();
      }
      
      checkAuthStatus();
    } catch (error) {
      // If refresh fails, logout user
      logout();
      throw error;
    }
  }, [userType, checkAuthStatus, logout]);

  return {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    refreshToken,
    checkAuthStatus,
  };
}

// Specific hooks for different user types
export function useCustomerAuth() {
  return useAuth('customer');
}

export function useManagerAuth() {
  return useAuth('manager');
}

export function useAdminAuth() {
  return useAuth('admin');
}
