/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    // NEXT_PUBLIC_BACKEND_URL: "https://dp.technixlabs.org/",
    // BACKEND_URL: "https://dp.technixlabs.org/",
    NEXT_PUBLIC_BACKEND_URL: "http://localhost:8000/",
    BACKEND_URL: "http://localhost:8000/",
    TENANT_MODE: "subdomain",
    PUBLIC_ROOT_DOMAIN: "localhost",
    //PUBLIC_ROOT_DOMAIN: "lvh.me",
  },
  crossOrigin: "anonymous",
  // Allow external hostnames for lvh.me
  // allowedDevOrigins: [
  //   // "*",
  //   // // "http://*.localhost:3000",
  //   // // "http://localhost.test:3000",
  //   // // "http://*.127.0.0.1:3000",
  //   // // "http://*.lvh.me",
  //   "http://localhost:3000",
  //   "http://lvh.me",
  //   "http://kips.localhost:3000",
  // ],
};

export default nextConfig;
